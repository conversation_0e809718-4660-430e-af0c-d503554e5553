// Attendance management functionality

// Attendance data
let attendanceData = {
    attendance: [],
    currentPage: 1,
    totalPages: 1,
    pageSize: 10,
    totalCount: 0,
    filters: {
        fromDate: '',
        toDate: '',
        employeeId: '',
        status: ''
    },
    summary: {
        present: 0,
        absent: 0,
        late: 0
    }
};

// Load attendance data
async function loadAttendanceData() {
    try {
        console.log('Loading attendance data...');
        
        // Load attendance summary
        await loadAttendanceSummary();
        
        // Load attendance records
        await loadAttendanceRecords();
        
        // Update UI
        updateAttendanceTable();
        updateAttendancePagination();
        updateAttendanceSummary();
        
        console.log('Attendance data loaded successfully');
    } catch (error) {
        console.error('Error loading attendance data:', error);
        loadMockAttendanceData();
        updateAttendanceTable();
        updateAttendancePagination();
        updateAttendanceSummary();
    }
}

// Load attendance summary
async function loadAttendanceSummary() {
    try {
        // Mock data for attendance summary
        attendanceData.summary = {
            present: 138,
            absent: 12,
            late: 8
        };
    } catch (error) {
        console.error('Error loading attendance summary:', error);
        attendanceData.summary = { present: 0, absent: 0, late: 0 };
    }
}

// Load attendance records
async function loadAttendanceRecords() {
    try {
        const params = {
            page: attendanceData.currentPage,
            limit: attendanceData.pageSize,
            fromDate: attendanceData.filters.fromDate,
            toDate: attendanceData.filters.toDate,
            employeeId: attendanceData.filters.employeeId,
            status: attendanceData.filters.status
        };
        
        // Remove empty parameters
        Object.keys(params).forEach(key => {
            if (!params[key]) delete params[key];
        });
        
        const response = await apiService.getAttendance(params);
        
        if (response && response.success) {
            attendanceData.attendance = response.data.attendance || [];
            attendanceData.totalCount = response.data.totalCount || 0;
            attendanceData.totalPages = Math.ceil(attendanceData.totalCount / attendanceData.pageSize);
        } else {
            loadMockAttendanceData();
        }
    } catch (error) {
        console.error('Error loading attendance records:', error);
        loadMockAttendanceData();
    }
}

// Load mock attendance data
function loadMockAttendanceData() {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    attendanceData.attendance = [
        {
            id: 1,
            employeeId: 1,
            employeeName: 'أحمد محمد علي',
            date: today.toISOString().split('T')[0],
            checkIn: '08:00',
            checkOut: '16:30',
            workingHours: '8:30',
            status: 'PRESENT',
            notes: ''
        },
        {
            id: 2,
            employeeId: 2,
            employeeName: 'سارة أحمد خالد',
            date: today.toISOString().split('T')[0],
            checkIn: '08:15',
            checkOut: '16:45',
            workingHours: '8:30',
            status: 'LATE',
            notes: 'تأخير 15 دقيقة'
        },
        {
            id: 3,
            employeeId: 3,
            employeeName: 'محمد علي حسن',
            date: today.toISOString().split('T')[0],
            checkIn: '07:45',
            checkOut: '16:15',
            workingHours: '8:30',
            status: 'PRESENT',
            notes: ''
        },
        {
            id: 4,
            employeeId: 4,
            employeeName: 'فاطمة خالد محمد',
            date: yesterday.toISOString().split('T')[0],
            checkIn: '',
            checkOut: '',
            workingHours: '0:00',
            status: 'ABSENT',
            notes: 'إجازة مرضية'
        },
        {
            id: 5,
            employeeId: 5,
            employeeName: 'عبدالله سعد الغامدي',
            date: yesterday.toISOString().split('T')[0],
            checkIn: '08:00',
            checkOut: '18:00',
            workingHours: '10:00',
            status: 'OVERTIME',
            notes: 'ساعات إضافية'
        }
    ];
    
    attendanceData.totalCount = attendanceData.attendance.length;
    attendanceData.totalPages = Math.ceil(attendanceData.totalCount / attendanceData.pageSize);
}

// Update attendance table
function updateAttendanceTable() {
    const tableBody = document.getElementById('attendanceTableBody');
    if (!tableBody) return;
    
    // Clear existing content
    tableBody.innerHTML = '';
    
    if (attendanceData.attendance.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted" style="padding: 40px;">
                    <i class="fas fa-clock" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <p>لا توجد سجلات حضور للعرض</p>
                </td>
            </tr>
        `;
        return;
    }
    
    // Create table rows
    const tableHTML = attendanceData.attendance.map(record => `
        <tr>
            <td><strong>${record.employeeName}</strong></td>
            <td>${formatDate(record.date)}</td>
            <td>${record.checkIn || '-'}</td>
            <td>${record.checkOut || '-'}</td>
            <td>${record.workingHours}</td>
            <td>
                <span class="badge ${getAttendanceStatusBadgeClass(record.status)}">
                    ${getAttendanceStatusText(record.status)}
                </span>
            </td>
            <td>${record.notes || '-'}</td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = tableHTML;
}

// Get attendance status badge class
function getAttendanceStatusBadgeClass(status) {
    const classes = {
        'PRESENT': 'badge-success',
        'ABSENT': 'badge-danger',
        'LATE': 'badge-warning',
        'HALF_DAY': 'badge-info',
        'OVERTIME': 'badge-primary'
    };
    return classes[status] || 'badge-secondary';
}

// Get attendance status text in Arabic
function getAttendanceStatusText(status) {
    const texts = {
        'PRESENT': 'حاضر',
        'ABSENT': 'غائب',
        'LATE': 'متأخر',
        'HALF_DAY': 'نصف يوم',
        'OVERTIME': 'ساعات إضافية'
    };
    return texts[status] || status;
}

// Update attendance pagination
function updateAttendancePagination() {
    // Similar to employees pagination
    const paginationContainer = document.querySelector('#attendance-page .pagination');
    if (!paginationContainer) return;
    
    // Clear existing content
    paginationContainer.innerHTML = '';
    
    if (attendanceData.totalPages <= 1) return;
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <button ${attendanceData.currentPage === 1 ? 'disabled' : ''} 
                onclick="changeAttendancePage(${attendanceData.currentPage - 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // Page numbers
    const startPage = Math.max(1, attendanceData.currentPage - 2);
    const endPage = Math.min(attendanceData.totalPages, attendanceData.currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button ${i === attendanceData.currentPage ? 'class="active"' : ''} 
                    onclick="changeAttendancePage(${i})">
                ${i}
            </button>
        `;
    }
    
    // Next button
    paginationHTML += `
        <button ${attendanceData.currentPage === attendanceData.totalPages ? 'disabled' : ''} 
                onclick="changeAttendancePage(${attendanceData.currentPage + 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// Update attendance summary
function updateAttendanceSummary() {
    const presentElement = document.getElementById('todayPresent');
    const absentElement = document.getElementById('todayAbsent');
    const lateElement = document.getElementById('todayLate');
    
    if (presentElement) {
        presentElement.textContent = attendanceData.summary.present;
    }
    
    if (absentElement) {
        absentElement.textContent = attendanceData.summary.absent;
    }
    
    if (lateElement) {
        lateElement.textContent = attendanceData.summary.late;
    }
}

// Change attendance page
function changeAttendancePage(page) {
    if (page < 1 || page > attendanceData.totalPages) return;
    
    attendanceData.currentPage = page;
    loadAttendanceData();
}

// Search attendance
function searchAttendance(searchTerm) {
    // For attendance, we might search by employee name
    console.log('Searching attendance for:', searchTerm);
    // Implementation would filter the attendance records
}

// Filter attendance
function filterAttendance() {
    const fromDate = document.getElementById('attendanceFromDate')?.value;
    const toDate = document.getElementById('attendanceToDate')?.value;
    
    attendanceData.filters.fromDate = fromDate;
    attendanceData.filters.toDate = toDate;
    attendanceData.currentPage = 1;
    
    loadAttendanceData();
}

// Show check-in modal
function showCheckInModal() {
    console.log('Show check-in modal');
    showInfo('سيتم إضافة نافذة تسجيل الدخول قريباً');
}

// Show check-out modal
function showCheckOutModal() {
    console.log('Show check-out modal');
    showInfo('سيتم إضافة نافذة تسجيل الخروج قريباً');
}

// Check in employee
async function checkInEmployee(employeeId, notes = '') {
    try {
        showLoading(true);
        
        const response = await apiService.checkIn(employeeId, notes);
        
        if (response && response.success) {
            showSuccess('تم تسجيل الدخول بنجاح');
            loadAttendanceData();
        } else {
            showError('فشل في تسجيل الدخول');
        }
    } catch (error) {
        console.error('Error checking in employee:', error);
        showError('خطأ في تسجيل الدخول');
    } finally {
        showLoading(false);
    }
}

// Check out employee
async function checkOutEmployee(employeeId, notes = '') {
    try {
        showLoading(true);
        
        const response = await apiService.checkOut(employeeId, notes);
        
        if (response && response.success) {
            showSuccess('تم تسجيل الخروج بنجاح');
            loadAttendanceData();
        } else {
            showError('فشل في تسجيل الخروج');
        }
    } catch (error) {
        console.error('Error checking out employee:', error);
        showError('خطأ في تسجيل الخروج');
    } finally {
        showLoading(false);
    }
}

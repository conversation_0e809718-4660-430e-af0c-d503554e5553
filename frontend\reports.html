<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .report-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .report-card.employees .report-icon {
            color: var(--info-color);
        }
        
        .report-card.attendance .report-icon {
            color: var(--success-color);
        }
        
        .report-card.leaves .report-icon {
            color: var(--warning-color);
        }
        
        .report-card.payroll .report-icon {
            color: var(--danger-color);
        }
        
        .report-card.performance .report-icon {
            color: var(--purple-color);
        }
        
        .report-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        
        .report-description {
            color: var(--text-muted);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .report-actions {
            display: flex;
            gap: 10px;
        }
        
        .report-parameters {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            margin-bottom: 30px;
            display: none;
        }
        
        .parameters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .parameter-group {
            display: flex;
            flex-direction: column;
        }
        
        .parameter-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .generated-reports {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
        }
        
        .report-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            margin-bottom: 10px;
            background: var(--gray-50);
        }
        
        .report-info {
            flex: 1;
        }
        
        .report-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }
        
        .report-meta {
            font-size: 0.875rem;
            color: var(--text-muted);
        }
        
        .report-actions-list {
            display: flex;
            gap: 5px;
        }
        
        .report-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .report-status.completed {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .report-status.processing {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .report-status.failed {
            background: var(--danger-light);
            color: var(--danger-dark);
        }
        
        .quick-reports {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .chart-container {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
        }
        
        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .parameters-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-reports {
                flex-direction: column;
            }
            
            .report-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .report-actions-list {
                width: 100%;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    الموظفين
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-clock"></i>
                    الحضور
                </a>
                <a href="leaves.html" class="nav-link">
                    <i class="fas fa-calendar-alt"></i>
                    الإجازات
                </a>
                <a href="reports.html" class="nav-link active">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="userName">المدير</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-chart-bar"></i>
                    التقارير والإحصائيات
                </h1>
                <p>إنشاء وإدارة التقارير المختلفة للنظام</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-info" onclick="refreshReports()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
                <button class="btn btn-secondary" onclick="showReportHistory()">
                    <i class="fas fa-history"></i>
                    سجل التقارير
                </button>
            </div>
        </div>

        <!-- Quick Reports -->
        <div class="quick-reports">
            <button class="btn btn-primary" onclick="generateQuickReport('daily_summary')">
                <i class="fas fa-calendar-day"></i>
                تقرير يومي سريع
            </button>
            <button class="btn btn-success" onclick="generateQuickReport('monthly_attendance')">
                <i class="fas fa-calendar-month"></i>
                حضور الشهر الحالي
            </button>
            <button class="btn btn-warning" onclick="generateQuickReport('pending_leaves')">
                <i class="fas fa-clock"></i>
                الإجازات المعلقة
            </button>
            <button class="btn btn-info" onclick="generateQuickReport('employee_count')">
                <i class="fas fa-users"></i>
                إحصائيات الموظفين
            </button>
        </div>

        <!-- Available Reports -->
        <div class="reports-grid">
            <div class="report-card employees" onclick="selectReport('employee_list')">
                <div class="report-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="report-title">تقرير الموظفين</div>
                <div class="report-description">
                    قائمة شاملة بجميع الموظفين مع بياناتهم الأساسية والوظيفية
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); selectReport('employee_list')">
                        <i class="fas fa-play"></i>
                        إنشاء
                    </button>
                </div>
            </div>

            <div class="report-card attendance" onclick="selectReport('attendance_report')">
                <div class="report-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="report-title">تقرير الحضور</div>
                <div class="report-description">
                    تقرير مفصل عن حضور وانصراف الموظفين لفترة محددة
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); selectReport('attendance_report')">
                        <i class="fas fa-play"></i>
                        إنشاء
                    </button>
                </div>
            </div>

            <div class="report-card leaves" onclick="selectReport('leaves_report')">
                <div class="report-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="report-title">تقرير الإجازات</div>
                <div class="report-description">
                    تقرير شامل عن طلبات الإجازات وأرصدة الموظفين
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); selectReport('leaves_report')">
                        <i class="fas fa-play"></i>
                        إنشاء
                    </button>
                </div>
            </div>

            <div class="report-card payroll" onclick="selectReport('payroll_report')">
                <div class="report-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="report-title">تقرير الرواتب</div>
                <div class="report-description">
                    تقرير مالي شامل عن رواتب ومستحقات الموظفين
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); selectReport('payroll_report')">
                        <i class="fas fa-play"></i>
                        إنشاء
                    </button>
                </div>
            </div>

            <div class="report-card performance" onclick="selectReport('performance_report')">
                <div class="report-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="report-title">تقرير الأداء</div>
                <div class="report-description">
                    تقييم أداء الموظفين والإحصائيات المتعلقة بالإنتاجية
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm" style="background: var(--purple-color); color: white;" onclick="event.stopPropagation(); selectReport('performance_report')">
                        <i class="fas fa-play"></i>
                        إنشاء
                    </button>
                </div>
            </div>

            <div class="report-card" onclick="selectReport('custom_report')">
                <div class="report-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="report-title">تقرير مخصص</div>
                <div class="report-description">
                    إنشاء تقرير مخصص بمعايير وحقول محددة حسب الحاجة
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm btn-secondary" onclick="event.stopPropagation(); selectReport('custom_report')">
                        <i class="fas fa-play"></i>
                        إنشاء
                    </button>
                </div>
            </div>
        </div>

        <!-- Report Parameters -->
        <div id="reportParameters" class="report-parameters">
            <h3>
                <i class="fas fa-sliders-h"></i>
                معايير التقرير
            </h3>
            <div class="parameters-grid">
                <div class="parameter-group">
                    <label for="reportFromDate">من تاريخ:</label>
                    <input type="date" id="reportFromDate" name="reportFromDate">
                </div>
                <div class="parameter-group">
                    <label for="reportToDate">إلى تاريخ:</label>
                    <input type="date" id="reportToDate" name="reportToDate">
                </div>
                <div class="parameter-group">
                    <label for="reportUnit">الوحدة:</label>
                    <select id="reportUnit" name="reportUnit">
                        <option value="">جميع الوحدات</option>
                    </select>
                </div>
                <div class="parameter-group">
                    <label for="reportRank">الرتبة:</label>
                    <select id="reportRank" name="reportRank">
                        <option value="">جميع الرتب</option>
                    </select>
                </div>
                <div class="parameter-group">
                    <label for="reportStatus">الحالة:</label>
                    <select id="reportStatus" name="reportStatus">
                        <option value="">جميع الحالات</option>
                        <option value="ACTIVE">نشط</option>
                        <option value="INACTIVE">غير نشط</option>
                    </select>
                </div>
                <div class="parameter-group">
                    <label for="reportFormat">صيغة التقرير:</label>
                    <select id="reportFormat" name="reportFormat">
                        <option value="PDF">PDF</option>
                        <option value="EXCEL">Excel</option>
                        <option value="CSV">CSV</option>
                    </select>
                </div>
            </div>
            <div class="report-actions">
                <button class="btn btn-secondary" onclick="cancelReportGeneration()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-file-alt"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>

        <!-- Generated Reports -->
        <div class="generated-reports">
            <h3>
                <i class="fas fa-file-alt"></i>
                التقارير المُنشأة
            </h3>
            <div id="reportsList">
                <!-- Generated reports will be loaded here -->
            </div>
            
            <!-- No Reports Message -->
            <div id="noReportsMessage" class="no-data-message" style="display: none;">
                <i class="fas fa-file-alt"></i>
                <h3>لا توجد تقارير</h3>
                <p>لم يتم إنشاء أي تقارير بعد</p>
                <button class="btn btn-primary" onclick="document.querySelector('.reports-grid').scrollIntoView()">
                    <i class="fas fa-plus"></i>
                    إنشاء تقرير جديد
                </button>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري إنشاء التقرير...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // Current selected report type
        let selectedReportType = null;
        
        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!isAuthenticated()) {
                redirectToLogin();
                return;
            }
            
            // Initialize reports page
            initializeReportsPage();
        });
        
        // Initialize reports page
        async function initializeReportsPage() {
            try {
                showLoading(true);
                
                // Load generated reports
                await loadGeneratedReports();
                
                // Load filter options
                loadReportFilterOptions();
                
                console.log('Reports page initialized successfully');
            } catch (error) {
                console.error('Error initializing reports page:', error);
                showError('خطأ في تحميل صفحة التقارير');
            } finally {
                showLoading(false);
            }
        }
        
        // Load report filter options
        function loadReportFilterOptions() {
            // Load units
            const unitSelect = document.getElementById('reportUnit');
            CONSTANTS.MILITARY_UNITS.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.value;
                option.textContent = unit.label;
                unitSelect.appendChild(option);
            });
            
            // Load ranks
            const rankSelect = document.getElementById('reportRank');
            CONSTANTS.MILITARY_RANKS.forEach(rank => {
                const option = document.createElement('option');
                option.value = rank.value;
                option.textContent = rank.label;
                rankSelect.appendChild(option);
            });
            
            // Set default dates
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('reportFromDate').value = firstDay.toISOString().split('T')[0];
            document.getElementById('reportToDate').value = today.toISOString().split('T')[0];
        }
        
        // Select report type
        function selectReport(reportType) {
            selectedReportType = reportType;
            document.getElementById('reportParameters').style.display = 'block';
            document.getElementById('reportParameters').scrollIntoView({ behavior: 'smooth' });
        }
        
        // Cancel report generation
        function cancelReportGeneration() {
            selectedReportType = null;
            document.getElementById('reportParameters').style.display = 'none';
        }
        
        // Generate report
        async function generateReport() {
            if (!selectedReportType) {
                showError('يرجى اختيار نوع التقرير');
                return;
            }
            
            try {
                showLoading(true);
                
                const parameters = {
                    reportType: selectedReportType,
                    fromDate: document.getElementById('reportFromDate').value,
                    toDate: document.getElementById('reportToDate').value,
                    unit: document.getElementById('reportUnit').value,
                    rank: document.getElementById('reportRank').value,
                    status: document.getElementById('reportStatus').value,
                    format: document.getElementById('reportFormat').value
                };
                
                const response = await apiService.generateReport(parameters);
                
                if (response && response.success) {
                    showSuccess('تم إنشاء التقرير بنجاح');
                    await loadGeneratedReports();
                    cancelReportGeneration();
                } else {
                    showError(response?.message || 'فشل في إنشاء التقرير');
                }
            } catch (error) {
                console.error('Error generating report:', error);
                showError('خطأ في إنشاء التقرير');
            } finally {
                showLoading(false);
            }
        }
        
        // Generate quick report
        async function generateQuickReport(reportType) {
            try {
                showLoading(true);
                
                const response = await apiService.generateQuickReport(reportType);
                
                if (response && response.success) {
                    showSuccess('تم إنشاء التقرير السريع بنجاح');
                    await loadGeneratedReports();
                } else {
                    showError(response?.message || 'فشل في إنشاء التقرير السريع');
                }
            } catch (error) {
                console.error('Error generating quick report:', error);
                showError('خطأ في إنشاء التقرير السريع');
            } finally {
                showLoading(false);
            }
        }
        
        // Load generated reports
        async function loadGeneratedReports() {
            try {
                const response = await apiService.getGeneratedReports();
                
                if (response && response.success) {
                    displayGeneratedReports(response.data.reports);
                } else {
                    console.error('Failed to load generated reports');
                }
            } catch (error) {
                console.error('Error loading generated reports:', error);
            }
        }
        
        // Display generated reports
        function displayGeneratedReports(reports) {
            const reportsList = document.getElementById('reportsList');
            const noReportsMessage = document.getElementById('noReportsMessage');
            
            if (!reports || reports.length === 0) {
                reportsList.innerHTML = '';
                noReportsMessage.style.display = 'block';
                return;
            }
            
            noReportsMessage.style.display = 'none';
            
            reportsList.innerHTML = reports.map(report => `
                <div class="report-item">
                    <div class="report-info">
                        <div class="report-name">${report.name}</div>
                        <div class="report-meta">
                            تم الإنشاء: ${formatDate(report.createdAt)} | 
                            الحجم: ${report.size || 'غير محدد'} |
                            الصيغة: ${report.format}
                            <span class="report-status ${report.status.toLowerCase()}">${getStatusText(report.status)}</span>
                        </div>
                    </div>
                    <div class="report-actions-list">
                        ${report.status === 'COMPLETED' ? `
                            <button class="btn btn-sm btn-success" onclick="downloadReport('${report.id}')">
                                <i class="fas fa-download"></i>
                                تحميل
                            </button>
                            <button class="btn btn-sm btn-info" onclick="viewReport('${report.id}')">
                                <i class="fas fa-eye"></i>
                                عرض
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-danger" onclick="deleteReport('${report.id}')">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        // Get status text in Arabic
        function getStatusText(status) {
            const statusMap = {
                'COMPLETED': 'مكتمل',
                'PROCESSING': 'قيد المعالجة',
                'FAILED': 'فشل'
            };
            return statusMap[status] || status;
        }
        
        // Download report
        async function downloadReport(reportId) {
            try {
                const response = await apiService.downloadReport(reportId);
                if (response && response.success) {
                    // Handle file download
                    showSuccess('تم تحميل التقرير بنجاح');
                } else {
                    showError('فشل في تحميل التقرير');
                }
            } catch (error) {
                console.error('Error downloading report:', error);
                showError('خطأ في تحميل التقرير');
            }
        }
        
        // View report
        async function viewReport(reportId) {
            try {
                const response = await apiService.viewReport(reportId);
                if (response && response.success) {
                    // Open report in new window/tab
                    window.open(response.data.viewUrl, '_blank');
                } else {
                    showError('فشل في عرض التقرير');
                }
            } catch (error) {
                console.error('Error viewing report:', error);
                showError('خطأ في عرض التقرير');
            }
        }
        
        // Delete report
        async function deleteReport(reportId) {
            if (!confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
                return;
            }
            
            try {
                const response = await apiService.deleteReport(reportId);
                if (response && response.success) {
                    showSuccess('تم حذف التقرير بنجاح');
                    await loadGeneratedReports();
                } else {
                    showError('فشل في حذف التقرير');
                }
            } catch (error) {
                console.error('Error deleting report:', error);
                showError('خطأ في حذف التقرير');
            }
        }
        
        // Refresh reports
        async function refreshReports() {
            await loadGeneratedReports();
            showSuccess('تم تحديث قائمة التقارير');
        }
        
        // Show report history
        function showReportHistory() {
            // This would typically open a modal or navigate to a history page
            showInfo('سيتم إضافة صفحة سجل التقارير قريباً');
        }
    </script>
</body>
</html>

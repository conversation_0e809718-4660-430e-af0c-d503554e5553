import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

import { User, UserDocument } from './schemas/user.schema';
import { 
  CreateUserDto, 
  UpdateUserDto, 
  ChangePasswordDto,
  UserRole,
  Permission,
  ROLE_PERMISSIONS
} from '@shared/types/user.types';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    // Check if username or email already exists
    const existingUser = await this.userModel.findOne({
      $or: [
        { username: createUserDto.username },
        { email: createUserDto.email }
      ]
    });

    if (existingUser) {
      if (existingUser.username === createUserDto.username) {
        throw new ConflictException('Username already exists');
      }
      if (existingUser.email === createUserDto.email) {
        throw new ConflictException('Email already exists');
      }
    }

    // Set default permissions based on role
    const permissions = createUserDto.permissions || ROLE_PERMISSIONS[createUserDto.role] || [];

    const user = new this.userModel({
      ...createUserDto,
      permissions
    });

    const savedUser = await user.save();
    return this.sanitizeUser(savedUser);
  }

  async findAll(page: number = 1, limit: number = 10, filters?: any): Promise<{ users: User[], total: number }> {
    const skip = (page - 1) * limit;
    const query = this.buildQuery(filters);

    const [users, total] = await Promise.all([
      this.userModel
        .find(query)
        .select('-password -refreshToken -passwordResetToken')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.userModel.countDocuments(query)
    ]);

    return {
      users: users.map(user => this.sanitizeUser(user)),
      total
    };
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userModel
      .findById(id)
      .select('-password -refreshToken -passwordResetToken')
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.sanitizeUser(user);
  }

  async findByUsername(username: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ username }).exec();
  }

  async findByEmail(email: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ email }).exec();
  }

  async findByRefreshToken(refreshToken: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ 
      refreshToken,
      refreshTokenExpires: { $gt: new Date() }
    }).exec();
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    // Check if username or email conflicts with other users
    if (updateUserDto.username || updateUserDto.email) {
      const conflictQuery: any = { _id: { $ne: id } };
      
      if (updateUserDto.username || updateUserDto.email) {
        conflictQuery.$or = [];
        if (updateUserDto.username) {
          conflictQuery.$or.push({ username: updateUserDto.username });
        }
        if (updateUserDto.email) {
          conflictQuery.$or.push({ email: updateUserDto.email });
        }
      }

      const existingUser = await this.userModel.findOne(conflictQuery);
      if (existingUser) {
        if (existingUser.username === updateUserDto.username) {
          throw new ConflictException('Username already exists');
        }
        if (existingUser.email === updateUserDto.email) {
          throw new ConflictException('Email already exists');
        }
      }
    }

    // Update permissions based on role if role is changed
    if (updateUserDto.role && !updateUserDto.permissions) {
      updateUserDto.permissions = ROLE_PERMISSIONS[updateUserDto.role] || [];
    }

    const user = await this.userModel
      .findByIdAndUpdate(id, updateUserDto, { new: true })
      .select('-password -refreshToken -passwordResetToken')
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.sanitizeUser(user);
  }

  async remove(id: string): Promise<void> {
    const result = await this.userModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('User not found');
    }
  }

  async changePassword(id: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(changePasswordDto.currentPassword);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Update password
    user.password = changePasswordDto.newPassword;
    await user.save();
  }

  async generatePasswordResetToken(email: string): Promise<string> {
    const user = await this.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const resetToken = crypto.randomBytes(32).toString('hex');
    const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

    user.passwordResetToken = hashedToken;
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    await user.save();

    return resetToken;
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
    
    const user = await this.userModel.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: new Date() }
    }).exec();

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    user.password = newPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();
  }

  async updateRefreshToken(id: string, refreshToken: string | null): Promise<void> {
    const updateData: any = {};
    
    if (refreshToken) {
      updateData.refreshToken = refreshToken;
      updateData.refreshTokenExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    } else {
      updateData.$unset = { refreshToken: 1, refreshTokenExpires: 1 };
    }

    await this.userModel.findByIdAndUpdate(id, updateData).exec();
  }

  async updateLastLogin(id: string): Promise<void> {
    await this.userModel.findByIdAndUpdate(id, { lastLogin: new Date() }).exec();
  }

  private buildQuery(filters?: any): any {
    const query: any = {};

    if (filters) {
      if (filters.role) {
        query.role = filters.role;
      }
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }
      if (filters.search) {
        query.$or = [
          { username: { $regex: filters.search, $options: 'i' } },
          { email: { $regex: filters.search, $options: 'i' } },
          { 'profile.fullName': { $regex: filters.search, $options: 'i' } }
        ];
      }
      if (filters.department) {
        query['profile.department'] = filters.department;
      }
    }

    return query;
  }

  private sanitizeUser(user: any): User {
    const userObj = user.toObject ? user.toObject() : user;
    delete userObj.password;
    delete userObj.refreshToken;
    delete userObj.passwordResetToken;
    delete userObj.passwordResetExpires;
    delete userObj.refreshTokenExpires;
    return userObj;
  }
}

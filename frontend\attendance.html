<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحضور - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .attendance-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .action-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .action-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .action-card.check-in .action-icon {
            color: var(--success-color);
        }
        
        .action-card.check-out .action-icon {
            color: var(--warning-color);
        }
        
        .time-display {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin: 10px 0;
        }
        
        .date-display {
            color: var(--text-muted);
            margin-bottom: 20px;
        }
        
        .attendance-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            text-align: center;
        }
        
        .summary-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .summary-card.present .summary-number {
            color: var(--success-color);
        }
        
        .summary-card.absent .summary-number {
            color: var(--danger-color);
        }
        
        .summary-card.late .summary-number {
            color: var(--warning-color);
        }
        
        .summary-card.overtime .summary-number {
            color: var(--info-color);
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .employee-search {
            flex: 1;
            min-width: 300px;
        }
        
        .employee-search input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 5px 5px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .search-result-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
        }
        
        .search-result-item:hover {
            background: var(--gray-50);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .attendance-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-badge.present {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-badge.absent {
            background: var(--danger-light);
            color: var(--danger-dark);
        }
        
        .status-badge.late {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-badge.overtime {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        @media (max-width: 768px) {
            .attendance-actions {
                grid-template-columns: 1fr;
            }
            
            .attendance-summary {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .quick-actions {
                flex-direction: column;
            }
            
            .employee-search {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    الموظفين
                </a>
                <a href="attendance.html" class="nav-link active">
                    <i class="fas fa-clock"></i>
                    الحضور
                </a>
                <a href="leaves.html" class="nav-link">
                    <i class="fas fa-calendar-alt"></i>
                    الإجازات
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="userName">المدير</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-clock"></i>
                    إدارة الحضور والانصراف
                </h1>
                <p>تسجيل ومتابعة حضور الموظفين</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-info" onclick="refreshAttendance()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
                <button class="btn btn-success" onclick="exportAttendance()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>

        <!-- Current Time and Date -->
        <div class="attendance-actions">
            <div class="action-card check-in">
                <div class="action-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h3>تسجيل الدخول</h3>
                <div class="time-display" id="currentTime">--:--:--</div>
                <div class="date-display" id="currentDate">-- / -- / ----</div>
                <button class="btn btn-success btn-lg" onclick="showCheckInModal()">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل دخول
                </button>
            </div>
            
            <div class="action-card check-out">
                <div class="action-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h3>تسجيل الخروج</h3>
                <div class="time-display" id="currentTime2">--:--:--</div>
                <div class="date-display" id="currentDate2">-- / -- / ----</div>
                <button class="btn btn-warning btn-lg" onclick="showCheckOutModal()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل خروج
                </button>
            </div>
        </div>

        <!-- Attendance Summary -->
        <div class="attendance-summary">
            <div class="summary-card present">
                <div class="summary-number" id="presentCount">0</div>
                <div class="summary-label">حاضر اليوم</div>
            </div>
            <div class="summary-card absent">
                <div class="summary-number" id="absentCount">0</div>
                <div class="summary-label">غائب اليوم</div>
            </div>
            <div class="summary-card late">
                <div class="summary-number" id="lateCount">0</div>
                <div class="summary-label">متأخر اليوم</div>
            </div>
            <div class="summary-card overtime">
                <div class="summary-number" id="overtimeCount">0</div>
                <div class="summary-label">ساعات إضافية</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="employee-search" style="position: relative;">
                <input type="text" id="employeeSearchInput" placeholder="البحث عن موظف للتسجيل السريع..." onkeyup="searchEmployees(this.value)">
                <div id="searchResults" class="search-results"></div>
            </div>
            <button class="btn btn-primary" onclick="bulkCheckIn()">
                <i class="fas fa-users"></i>
                تسجيل جماعي - دخول
            </button>
            <button class="btn btn-secondary" onclick="bulkCheckOut()">
                <i class="fas fa-users"></i>
                تسجيل جماعي - خروج
            </button>
        </div>

        <!-- Attendance Filters -->
        <div class="attendance-filters">
            <div class="filter-group">
                <label>التاريخ من:</label>
                <input type="date" id="fromDate" onchange="filterAttendance()">
            </div>
            <div class="filter-group">
                <label>إلى:</label>
                <input type="date" id="toDate" onchange="filterAttendance()">
            </div>
            <div class="filter-group">
                <label>الوحدة:</label>
                <select id="unitFilter" onchange="filterAttendance()">
                    <option value="">جميع الوحدات</option>
                </select>
            </div>
            <div class="filter-group">
                <label>الحالة:</label>
                <select id="statusFilter" onchange="filterAttendance()">
                    <option value="">جميع الحالات</option>
                    <option value="PRESENT">حاضر</option>
                    <option value="ABSENT">غائب</option>
                    <option value="LATE">متأخر</option>
                    <option value="OVERTIME">ساعات إضافية</option>
                </select>
            </div>
            <button class="btn btn-secondary" onclick="clearAttendanceFilters()">
                <i class="fas fa-times"></i>
                مسح الفلاتر
            </button>
        </div>

        <!-- Attendance Table -->
        <div class="table-container">
            <div class="table-header">
                <div class="table-title">
                    <h3>سجل الحضور</h3>
                    <span class="record-count">عدد السجلات: <span id="attendanceRecordCount">0</span></span>
                </div>
            </div>

            <table class="data-table" id="attendanceTable">
                <thead>
                    <tr>
                        <th onclick="sortAttendanceTable('employeeName')" class="sortable">
                            اسم الموظف
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortAttendanceTable('date')" class="sortable">
                            التاريخ
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortAttendanceTable('checkIn')" class="sortable">
                            وقت الدخول
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortAttendanceTable('checkOut')" class="sortable">
                            وقت الخروج
                            <i class="fas fa-sort"></i>
                        </th>
                        <th>ساعات العمل</th>
                        <th onclick="sortAttendanceTable('status')" class="sortable">
                            الحالة
                            <i class="fas fa-sort"></i>
                        </th>
                        <th>الملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="attendanceTableBody">
                    <!-- Attendance data will be loaded here -->
                </tbody>
            </table>

            <!-- No Data Message -->
            <div id="noAttendanceData" class="no-data-message" style="display: none;">
                <i class="fas fa-clock"></i>
                <h3>لا توجد بيانات حضور</h3>
                <p>لم يتم العثور على أي سجلات حضور للفترة المحددة</p>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span>عرض <span id="attendanceShowingFrom">1</span> إلى <span id="attendanceShowingTo">10</span> من <span id="attendanceTotalRecords">0</span> سجل</span>
            </div>
            <div class="pagination" id="attendancePagination">
                <!-- Pagination will be rendered here -->
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/attendance.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!isAuthenticated()) {
                redirectToLogin();
                return;
            }
            
            // Initialize attendance page
            initializeAttendancePage();
            
            // Start real-time clock
            startClock();
        });
        
        // Initialize attendance page
        async function initializeAttendancePage() {
            try {
                showLoading(true);
                
                // Load attendance data
                await loadAttendanceData();
                
                // Load filter options
                loadAttendanceFilterOptions();
                
                // Update summary
                updateAttendanceSummary();
                
                console.log('Attendance page initialized successfully');
            } catch (error) {
                console.error('Error initializing attendance page:', error);
                showError('خطأ في تحميل صفحة الحضور');
            } finally {
                showLoading(false);
            }
        }
        
        // Start real-time clock
        function startClock() {
            function updateClock() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA');
                const dateString = now.toLocaleDateString('ar-SA');
                
                document.getElementById('currentTime').textContent = timeString;
                document.getElementById('currentTime2').textContent = timeString;
                document.getElementById('currentDate').textContent = dateString;
                document.getElementById('currentDate2').textContent = dateString;
            }
            
            updateClock();
            setInterval(updateClock, 1000);
        }
        
        // Load attendance filter options
        function loadAttendanceFilterOptions() {
            // Load units for filter
            const unitFilter = document.getElementById('unitFilter');
            CONSTANTS.MILITARY_UNITS.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.value;
                option.textContent = unit.label;
                unitFilter.appendChild(option);
            });
            
            // Set default dates
            const today = new Date();
            const fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('fromDate').value = fromDate.toISOString().split('T')[0];
            document.getElementById('toDate').value = today.toISOString().split('T')[0];
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين</span>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="#" class="nav-link" data-page="employees">
                    <i class="fas fa-users"></i>
                    الموظفين
                </a>
                <a href="#" class="nav-link" data-page="attendance">
                    <i class="fas fa-clock"></i>
                    الحضور
                </a>
                <a href="#" class="nav-link" data-page="leaves">
                    <i class="fas fa-calendar-alt"></i>
                    الإجازات
                </a>
                <a href="#" class="nav-link" data-page="reports">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="userName">المدير</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page active">
            <div class="page-header">
                <h1>
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </h1>
                <p>نظرة عامة على النظام والإحصائيات</p>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmployees">0</h3>
                        <p>إجمالي الموظفين</p>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeEmployees">0</h3>
                        <p>الموظفين النشطين</p>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="presentToday">0</h3>
                        <p>الحاضرين اليوم</p>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="pendingLeaves">0</h3>
                        <p>طلبات الإجازة المعلقة</p>
                    </div>
                </div>
            </div>

            <!-- Charts and Recent Activity -->
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-chart-pie"></i>
                            توزيع الموظفين حسب الوحدة
                        </h3>
                    </div>
                    <div class="card-content">
                        <div id="unitChart" class="chart-container">
                            <!-- Chart will be rendered here -->
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-list"></i>
                            النشاطات الأخيرة
                        </h3>
                    </div>
                    <div class="card-content">
                        <div id="recentActivity" class="activity-list">
                            <!-- Recent activities will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3>
                    <i class="fas fa-bolt"></i>
                    إجراءات سريعة
                </h3>
                <div class="actions-grid">
                    <button class="action-btn primary" onclick="showPage('employees')">
                        <i class="fas fa-user-plus"></i>
                        إضافة موظف جديد
                    </button>
                    <button class="action-btn success" onclick="showPage('attendance')">
                        <i class="fas fa-clock"></i>
                        تسجيل حضور
                    </button>
                    <button class="action-btn warning" onclick="showPage('leaves')">
                        <i class="fas fa-calendar-plus"></i>
                        طلب إجازة
                    </button>
                    <button class="action-btn info" onclick="showPage('reports')">
                        <i class="fas fa-file-alt"></i>
                        إنشاء تقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Employees Page -->
        <div id="employees-page" class="page">
            <div class="page-header">
                <h1>
                    <i class="fas fa-users"></i>
                    إدارة الموظفين
                </h1>
                <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                    <i class="fas fa-plus"></i>
                    إضافة موظف جديد
                </button>
            </div>

            <!-- Search and Filters -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="employeeSearch" placeholder="البحث عن موظف...">
                </div>
                <div class="filter-group">
                    <select id="unitFilter">
                        <option value="">جميع الوحدات</option>
                    </select>
                    <select id="rankFilter">
                        <option value="">جميع الرتب</option>
                    </select>
                    <select id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="ACTIVE">نشط</option>
                        <option value="INACTIVE">غير نشط</option>
                    </select>
                </div>
            </div>

            <!-- Employees Table -->
            <div class="table-container">
                <table class="data-table" id="employeesTable">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>الاسم الكامل</th>
                            <th>الرقم العسكري</th>
                            <th>الرتبة</th>
                            <th>الوحدة</th>
                            <th>المنصب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        <!-- Employee data will be loaded here -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination" id="employeesPagination">
                <!-- Pagination will be rendered here -->
            </div>
        </div>

        <!-- Attendance Page -->
        <div id="attendance-page" class="page">
            <div class="page-header">
                <h1>
                    <i class="fas fa-clock"></i>
                    إدارة الحضور
                </h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="showCheckInModal()">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل دخول
                    </button>
                    <button class="btn btn-warning" onclick="showCheckOutModal()">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل خروج
                    </button>
                </div>
            </div>

            <!-- Attendance Summary -->
            <div class="attendance-summary">
                <div class="summary-card">
                    <h3>الحضور اليوم</h3>
                    <div class="summary-stats">
                        <div class="stat">
                            <span class="stat-number" id="todayPresent">0</span>
                            <span class="stat-label">حاضر</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="todayAbsent">0</span>
                            <span class="stat-label">غائب</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="todayLate">0</span>
                            <span class="stat-label">متأخر</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Filters -->
            <div class="filters-section">
                <div class="date-filters">
                    <input type="date" id="attendanceFromDate">
                    <input type="date" id="attendanceToDate">
                    <button class="btn btn-primary" onclick="filterAttendance()">
                        <i class="fas fa-filter"></i>
                        فلترة
                    </button>
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="table-container">
                <table class="data-table" id="attendanceTable">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>التاريخ</th>
                            <th>وقت الدخول</th>
                            <th>وقت الخروج</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="attendanceTableBody">
                        <!-- Attendance data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Leaves Page -->
        <div id="leaves-page" class="page">
            <div class="page-header">
                <h1>
                    <i class="fas fa-calendar-alt"></i>
                    إدارة الإجازات
                </h1>
                <button class="btn btn-primary" onclick="showAddLeaveModal()">
                    <i class="fas fa-plus"></i>
                    طلب إجازة جديدة
                </button>
            </div>

            <!-- Leave Summary -->
            <div class="leave-summary">
                <div class="summary-card">
                    <h3>ملخص الإجازات</h3>
                    <div class="summary-stats">
                        <div class="stat">
                            <span class="stat-number" id="pendingLeavesCount">0</span>
                            <span class="stat-label">معلقة</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="approvedLeavesCount">0</span>
                            <span class="stat-label">موافق عليها</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="rejectedLeavesCount">0</span>
                            <span class="stat-label">مرفوضة</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leaves Table -->
            <div class="table-container">
                <table class="data-table" id="leavesTable">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="leavesTableBody">
                        <!-- Leave data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Reports Page -->
        <div id="reports-page" class="page">
            <div class="page-header">
                <h1>
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </h1>
                <p>إنشاء وتحميل التقارير المختلفة</p>
            </div>

            <!-- Report Types -->
            <div class="reports-grid">
                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>تقرير الموظفين</h3>
                    <p>قائمة شاملة بجميع الموظفين وبياناتهم</p>
                    <button class="btn btn-primary" onclick="generateReport('EMPLOYEE_LIST')">
                        إنشاء التقرير
                    </button>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>تقرير الحضور</h3>
                    <p>تقرير مفصل عن حضور الموظفين</p>
                    <button class="btn btn-primary" onclick="generateReport('ATTENDANCE')">
                        إنشاء التقرير
                    </button>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3>تقرير الإجازات</h3>
                    <p>تقرير عن طلبات الإجازات والموافقات</p>
                    <button class="btn btn-primary" onclick="generateReport('LEAVES')">
                        إنشاء التقرير
                    </button>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3>تقرير الإحصائيات</h3>
                    <p>إحصائيات شاملة عن النظام</p>
                    <button class="btn btn-primary" onclick="generateReport('STATISTICS')">
                        إنشاء التقرير
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Check authentication before loading page
        function checkAuth() {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');

            console.log('Checking authentication...');
            console.log('Token:', token);
            console.log('User data:', userData);

            if (!token || !userData) {
                console.log('No authentication found, redirecting to login...');
                window.location.href = 'login.html';
                return false;
            }

            try {
                const user = JSON.parse(userData);
                document.getElementById('userName').textContent = user.username || 'المستخدم';
                console.log('Authentication valid, user:', user);
                return true;
            } catch (e) {
                console.error('Invalid user data, redirecting to login...');
                localStorage.clear();
                window.location.href = 'login.html';
                return false;
            }
        }

        // Logout function
        function logout() {
            console.log('Logging out...');
            localStorage.clear();
            window.location.href = 'login.html';
        }

        // Check auth on page load
        if (!checkAuth()) {
            // Stop loading other scripts if not authenticated
            document.write('<script>console.log("Authentication failed, stopping script loading");<\/script>');
        }
    </script>
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/employees.js"></script>
    <script src="js/attendance.js"></script>
    <script src="js/leaves.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/main.js"></script>
</body>
</html>

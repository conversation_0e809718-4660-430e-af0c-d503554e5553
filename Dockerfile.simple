# Simple Node.js Dockerfile for testing
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY test-package.json package.json

# Install dependencies
RUN npm install

# Copy application files
COPY test-server.js server.js
COPY frontend/ frontend/

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "server.js"]

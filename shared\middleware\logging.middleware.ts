import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth.middleware';

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    const { method, originalUrl, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const userId = req.user?._id || 'anonymous';
    const username = req.user?.username || 'anonymous';

    const startTime = Date.now();

    // Log request
    this.logger.log(
      `[${method}] ${originalUrl} - ${ip} - ${userAgent} - User: ${username} (${userId})`
    );

    // Override res.end to log response
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      const { statusCode } = res;
      
      const logLevel = statusCode >= 400 ? 'error' : 'log';
      const logger = new Logger('HTTP');
      
      logger[logLevel](
        `[${method}] ${originalUrl} - ${statusCode} - ${duration}ms - User: ${username} (${userId})`
      );

      // Log request body for POST/PUT/PATCH requests (excluding sensitive data)
      if (['POST', 'PUT', 'PATCH'].includes(method) && req.body) {
        const sanitizedBody = LoggingMiddleware.sanitizeRequestBody(req.body);
        logger.debug(`Request Body: ${JSON.stringify(sanitizedBody)}`);
      }

      originalEnd.call(this, chunk, encoding);
    };

    next();
  }

  private static sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sensitiveFields = [
      'password',
      'currentPassword',
      'newPassword',
      'token',
      'refreshToken',
      'accessToken',
      'secret',
      'key',
      'authorization'
    ];

    const sanitized = { ...body };

    const sanitizeObject = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(item => sanitizeObject(item));
      }

      if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            result[key] = '[REDACTED]';
          } else if (typeof value === 'object') {
            result[key] = sanitizeObject(value);
          } else {
            result[key] = value;
          }
        }
        return result;
      }

      return obj;
    };

    return sanitizeObject(sanitized);
  }
}

// Audit Log Interface
export interface AuditLog {
  _id?: string;
  userId: string;
  username: string;
  action: AuditAction;
  resource: string;
  resourceId?: string;
  details: any;
  ip: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

export enum AuditAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  GENERATE = 'GENERATE',
  SCAN = 'SCAN'
}

// Audit Logger Service
@Injectable()
export class AuditLogger {
  private readonly logger = new Logger('AUDIT');

  async log(auditLog: Omit<AuditLog, '_id' | 'timestamp'>): Promise<void> {
    const logEntry: AuditLog = {
      ...auditLog,
      timestamp: new Date()
    };

    // Log to console/file
    this.logger.log(
      `[${auditLog.action}] ${auditLog.resource} - User: ${auditLog.username} (${auditLog.userId}) - Success: ${auditLog.success}`
    );

    if (auditLog.details) {
      this.logger.debug(`Details: ${JSON.stringify(auditLog.details)}`);
    }

    if (!auditLog.success && auditLog.errorMessage) {
      this.logger.error(`Error: ${auditLog.errorMessage}`);
    }

    // Here you would typically save to database
    // await this.auditLogRepository.save(logEntry);
  }

  async logUserAction(
    req: AuthenticatedRequest,
    action: AuditAction,
    resource: string,
    resourceId?: string,
    details?: any,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    if (!req.user) {
      return;
    }

    await this.log({
      userId: req.user._id!,
      username: req.user.username,
      action,
      resource,
      resourceId,
      details,
      ip: req.ip,
      userAgent: req.headers['user-agent'] || '',
      success,
      errorMessage
    });
  }
}

// Decorator for automatic audit logging
export function AuditLog(action: AuditAction, resource: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const req = args.find(arg => arg && arg.user);
      const auditLogger = new AuditLogger();
      
      try {
        const result = await method.apply(this, args);
        
        if (req) {
          await auditLogger.logUserAction(
            req,
            action,
            resource,
            result?._id || result?.id,
            { input: args.slice(1) }, // Exclude req from details
            true
          );
        }
        
        return result;
      } catch (error) {
        if (req) {
          await auditLogger.logUserAction(
            req,
            action,
            resource,
            undefined,
            { input: args.slice(1), error: error.message },
            false,
            error.message
          );
        }
        throw error;
      }
    };
  };
}

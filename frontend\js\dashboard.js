// Dashboard functionality for Employee Management System

// Dashboard data
let dashboardData = {
    statistics: {
        totalEmployees: 0,
        activeEmployees: 0,
        presentToday: 0,
        pendingLeaves: 0
    },
    unitDistribution: [],
    recentActivities: []
};

// Load dashboard data
async function loadDashboardData() {
    try {
        console.log('Loading dashboard data...');
        
        // Load statistics
        await loadDashboardStatistics();
        
        // Load unit distribution
        await loadUnitDistribution();
        
        // Load recent activities
        await loadRecentActivities();
        
        // Update dashboard UI
        updateDashboardUI();
        
        console.log('Dashboard data loaded successfully');
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showError('خطأ في تحميل بيانات لوحة التحكم');
    }
}

// Load dashboard statistics
async function loadDashboardStatistics() {
    try {
        // Try to get real statistics from API
        const response = await apiService.getEmployeeStatistics();
        
        if (response && response.success) {
            dashboardData.statistics = {
                totalEmployees: response.data.totalEmployees || 0,
                activeEmployees: response.data.activeEmployees || 0,
                presentToday: response.data.presentToday || 0,
                pendingLeaves: response.data.pendingLeaves || 0
            };
        } else {
            // Use mock data if API fails
            dashboardData.statistics = {
                totalEmployees: 150,
                activeEmployees: 142,
                presentToday: 138,
                pendingLeaves: 12
            };
        }
    } catch (error) {
        console.error('Error loading statistics:', error);
        // Use mock data as fallback
        dashboardData.statistics = {
            totalEmployees: 150,
            activeEmployees: 142,
            presentToday: 138,
            pendingLeaves: 12
        };
    }
}

// Load unit distribution data
async function loadUnitDistribution() {
    try {
        // Mock data for unit distribution
        dashboardData.unitDistribution = [
            { unit: 'الوحدة الأولى', count: 45, percentage: 30 },
            { unit: 'الوحدة الثانية', count: 38, percentage: 25 },
            { unit: 'وحدة الاستطلاع', count: 25, percentage: 17 },
            { unit: 'وحدة الإشارة', count: 20, percentage: 13 },
            { unit: 'وحدة الهندسة', count: 15, percentage: 10 },
            { unit: 'أخرى', count: 7, percentage: 5 }
        ];
    } catch (error) {
        console.error('Error loading unit distribution:', error);
        dashboardData.unitDistribution = [];
    }
}

// Load recent activities
async function loadRecentActivities() {
    try {
        // Mock data for recent activities
        dashboardData.recentActivities = [
            {
                id: 1,
                type: 'employee_added',
                icon: 'success',
                title: 'إضافة موظف جديد',
                description: 'تم إضافة الموظف أحمد محمد إلى النظام',
                time: 'منذ 5 دقائق',
                timestamp: new Date(Date.now() - 5 * 60 * 1000)
            },
            {
                id: 2,
                type: 'leave_approved',
                icon: 'info',
                title: 'موافقة على إجازة',
                description: 'تم الموافقة على طلب إجازة سارة أحمد',
                time: 'منذ 15 دقيقة',
                timestamp: new Date(Date.now() - 15 * 60 * 1000)
            },
            {
                id: 3,
                type: 'attendance_late',
                icon: 'warning',
                title: 'تأخير في الحضور',
                description: 'تأخر محمد علي عن موعد الحضور',
                time: 'منذ 30 دقيقة',
                timestamp: new Date(Date.now() - 30 * 60 * 1000)
            },
            {
                id: 4,
                type: 'report_generated',
                icon: 'success',
                title: 'إنشاء تقرير',
                description: 'تم إنشاء تقرير الحضور الشهري',
                time: 'منذ ساعة',
                timestamp: new Date(Date.now() - 60 * 60 * 1000)
            },
            {
                id: 5,
                type: 'employee_updated',
                icon: 'info',
                title: 'تحديث بيانات موظف',
                description: 'تم تحديث بيانات الموظف فاطمة خالد',
                time: 'منذ ساعتين',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
            }
        ];
    } catch (error) {
        console.error('Error loading recent activities:', error);
        dashboardData.recentActivities = [];
    }
}

// Update dashboard UI
function updateDashboardUI() {
    updateStatisticsCards();
    updateUnitChart();
    updateRecentActivities();
}

// Update statistics cards
function updateStatisticsCards() {
    const stats = dashboardData.statistics;
    
    // Update total employees
    const totalEmployeesElement = document.getElementById('totalEmployees');
    if (totalEmployeesElement) {
        animateNumber(totalEmployeesElement, stats.totalEmployees);
    }
    
    // Update active employees
    const activeEmployeesElement = document.getElementById('activeEmployees');
    if (activeEmployeesElement) {
        animateNumber(activeEmployeesElement, stats.activeEmployees);
    }
    
    // Update present today
    const presentTodayElement = document.getElementById('presentToday');
    if (presentTodayElement) {
        animateNumber(presentTodayElement, stats.presentToday);
    }
    
    // Update pending leaves
    const pendingLeavesElement = document.getElementById('pendingLeaves');
    if (pendingLeavesElement) {
        animateNumber(pendingLeavesElement, stats.pendingLeaves);
    }
}

// Animate number counting
function animateNumber(element, targetNumber) {
    const startNumber = 0;
    const duration = 1000; // 1 second
    const startTime = Date.now();
    
    function updateNumber() {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
        element.textContent = currentNumber.toLocaleString('ar-SA');
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    updateNumber();
}

// Update unit chart
function updateUnitChart() {
    const chartContainer = document.getElementById('unitChart');
    if (!chartContainer) return;
    
    // Clear existing content
    chartContainer.innerHTML = '';
    
    if (dashboardData.unitDistribution.length === 0) {
        chartContainer.innerHTML = '<p class="text-muted">لا توجد بيانات للعرض</p>';
        return;
    }
    
    // Create simple bar chart
    const chartHTML = dashboardData.unitDistribution.map(item => `
        <div class="chart-item" style="margin-bottom: 12px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                <span style="font-size: 14px; font-weight: 500;">${item.unit}</span>
                <span style="font-size: 12px; color: #64748b;">${item.count} (${item.percentage}%)</span>
            </div>
            <div style="background: #e2e8f0; border-radius: 4px; height: 8px; overflow: hidden;">
                <div style="background: var(--primary-color); height: 100%; width: ${item.percentage}%; transition: width 0.5s ease;"></div>
            </div>
        </div>
    `).join('');
    
    chartContainer.innerHTML = chartHTML;
}

// Update recent activities
function updateRecentActivities() {
    const activitiesContainer = document.getElementById('recentActivity');
    if (!activitiesContainer) return;
    
    // Clear existing content
    activitiesContainer.innerHTML = '';
    
    if (dashboardData.recentActivities.length === 0) {
        activitiesContainer.innerHTML = '<p class="text-muted">لا توجد أنشطة حديثة</p>';
        return;
    }
    
    // Create activities HTML
    const activitiesHTML = dashboardData.recentActivities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon ${activity.icon}">
                <i class="fas ${getActivityIcon(activity.type)}"></i>
            </div>
            <div class="activity-content">
                <h4>${activity.title}</h4>
                <p>${activity.description}</p>
            </div>
            <div class="activity-time">
                ${activity.time}
            </div>
        </div>
    `).join('');
    
    activitiesContainer.innerHTML = activitiesHTML;
}

// Get activity icon based on type
function getActivityIcon(type) {
    const icons = {
        employee_added: 'fa-user-plus',
        employee_updated: 'fa-user-edit',
        employee_deleted: 'fa-user-minus',
        leave_approved: 'fa-check-circle',
        leave_rejected: 'fa-times-circle',
        leave_requested: 'fa-calendar-plus',
        attendance_late: 'fa-clock',
        attendance_absent: 'fa-user-times',
        report_generated: 'fa-file-alt',
        system_backup: 'fa-database',
        user_login: 'fa-sign-in-alt',
        user_logout: 'fa-sign-out-alt'
    };
    
    return icons[type] || 'fa-info-circle';
}

// Refresh dashboard data
async function refreshDashboard() {
    try {
        showLoading(true);
        await loadDashboardData();
        showSuccess('تم تحديث لوحة التحكم بنجاح');
    } catch (error) {
        console.error('Error refreshing dashboard:', error);
        showError('خطأ في تحديث لوحة التحكم');
    } finally {
        showLoading(false);
    }
}

// Auto-refresh dashboard every 5 minutes
let dashboardRefreshInterval;

function startDashboardAutoRefresh() {
    // Clear existing interval
    if (dashboardRefreshInterval) {
        clearInterval(dashboardRefreshInterval);
    }
    
    // Set new interval (5 minutes)
    dashboardRefreshInterval = setInterval(() => {
        if (currentPage === 'dashboard') {
            refreshDashboard();
        }
    }, 5 * 60 * 1000);
}

function stopDashboardAutoRefresh() {
    if (dashboardRefreshInterval) {
        clearInterval(dashboardRefreshInterval);
        dashboardRefreshInterval = null;
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Start auto-refresh when dashboard is active
    if (currentPage === 'dashboard') {
        startDashboardAutoRefresh();
    }
});

// Stop auto-refresh when page unloads
window.addEventListener('beforeunload', function() {
    stopDashboardAutoRefresh();
});

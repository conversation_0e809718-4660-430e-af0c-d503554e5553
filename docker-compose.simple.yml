version: '3.8'

services:
  # Simple Node.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: employee_app_simple
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app/frontend:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  default:
    driver: bridge

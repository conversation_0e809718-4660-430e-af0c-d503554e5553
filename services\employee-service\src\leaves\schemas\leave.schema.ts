import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { LeaveType, LeaveStatus } from '@shared/types/employee.types';

export type LeaveDocument = Leave & Document;

@Schema({
  timestamps: true,
  collection: 'leaves'
})
export class Leave {
  @Prop({ type: Types.ObjectId, ref: 'Employee', required: true })
  employeeId: Types.ObjectId;

  @Prop({ 
    required: true, 
    enum: Object.values(LeaveType) 
  })
  type: LeaveType;

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  endDate: Date;

  @Prop({ required: true, min: 1 })
  totalDays: number;

  @Prop({ required: true })
  reason: string;

  @Prop()
  description: string;

  @Prop({ 
    required: true, 
    enum: Object.values(LeaveStatus),
    default: LeaveStatus.PENDING 
  })
  status: LeaveStatus;

  @Prop()
  approvedBy: string;

  @Prop()
  approvedAt: Date;

  @Prop()
  rejectedBy: string;

  @Prop()
  rejectedAt: Date;

  @Prop()
  rejectionReason: string;

  @Prop()
  cancelledBy: string;

  @Prop()
  cancelledAt: Date;

  @Prop()
  cancellationReason: string;

  @Prop({ required: true })
  requestedBy: string;

  @Prop()
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };

  @Prop()
  medicalCertificate: string; // File path for medical leaves

  @Prop([String])
  attachments: string[]; // Supporting documents

  @Prop()
  replacementEmployee: string; // Employee covering duties

  @Prop()
  handoverNotes: string;

  @Prop({ default: false })
  isEmergency: boolean;

  @Prop()
  returnDate: Date; // Actual return date

  @Prop()
  extensionRequested: boolean;

  @Prop()
  extensionDays: number;

  @Prop()
  extensionReason: string;

  @Prop()
  comments: [{
    author: string;
    message: string;
    timestamp: Date;
  }];
}

export const LeaveSchema = SchemaFactory.createForClass(Leave);

// Indexes
LeaveSchema.index({ employeeId: 1 });
LeaveSchema.index({ status: 1 });
LeaveSchema.index({ type: 1 });
LeaveSchema.index({ startDate: 1 });
LeaveSchema.index({ endDate: 1 });
LeaveSchema.index({ requestedBy: 1 });
LeaveSchema.index({ approvedBy: 1 });

// Pre-save middleware to calculate total days
LeaveSchema.pre('save', function(next) {
  if (this.startDate && this.endDate) {
    const start = new Date(this.startDate);
    const end = new Date(this.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // Include both start and end dates
    this.totalDays = diffDays;
  }
  next();
});

// Virtual for leave duration in a readable format
LeaveSchema.virtual('duration').get(function() {
  if (this.totalDays === 1) {
    return '1 day';
  }
  return `${this.totalDays} days`;
});

// Virtual for checking if leave is current
LeaveSchema.virtual('isCurrent').get(function() {
  const now = new Date();
  return this.startDate <= now && this.endDate >= now && this.status === LeaveStatus.APPROVED;
});

// Virtual for checking if leave is upcoming
LeaveSchema.virtual('isUpcoming').get(function() {
  const now = new Date();
  return this.startDate > now && this.status === LeaveStatus.APPROVED;
});

// Virtual for checking if leave is overdue (past end date but no return recorded)
LeaveSchema.virtual('isOverdue').get(function() {
  const now = new Date();
  return this.endDate < now && this.status === LeaveStatus.APPROVED && !this.returnDate;
});

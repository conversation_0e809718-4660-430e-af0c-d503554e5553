const http = require('http');
const url = require('url');

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        resolve({});
      }
    });
    req.on('error', reject);
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Mock data
const mockEmployees = [
  {
    id: '1',
    personalInfo: {
      fullName: 'أحمد محمد علي',
      militaryNumber: 'M001',
      nationalId: '1234567890',
      phoneNumber: '0501234567',
      email: '<EMAIL>'
    },
    jobInfo: {
      rank: 'رقيب أول',
      unit: 'الوحدة الأولى',
      position: 'مسؤول إداري',
      salary: 8000,
      appointmentDate: '2020-01-15'
    },
    status: 'ACTIVE'
  },
  {
    id: '2',
    personalInfo: {
      fullName: 'محمد أحمد سالم',
      militaryNumber: 'M002',
      nationalId: '0987654321',
      phoneNumber: '0507654321',
      email: '<EMAIL>'
    },
    jobInfo: {
      rank: 'عريف',
      unit: 'الوحدة الثانية',
      position: 'مسؤول أمن',
      salary: 6500,
      appointmentDate: '2021-03-10'
    },
    status: 'ACTIVE'
  }
];

const mockUsers = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123', // في التطبيق الحقيقي، يجب تشفير كلمة المرور
    role: 'ADMIN',
    permissions: ['READ_EMPLOYEES', 'MANAGE_EMPLOYEES', 'READ_ATTENDANCE', 'MANAGE_ATTENDANCE', 'READ_LEAVES', 'MANAGE_LEAVES', 'APPROVE_LEAVES', 'GENERATE_REPORTS']
  }
];

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        message: 'Employee Management System is running!',
        timestamp: new Date().toISOString(),
        services: {
          auth: 'running',
          employees: 'running',
          reports: 'running'
        }
      });
      return;
    }

    // Auth login
    if (path === '/api/v1/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { username, password } = body;

      const user = mockUsers.find(u => u.username === username && u.password === password);

      if (!user) {
        sendJSON(res, 401, {
          success: false,
          message: 'Invalid credentials'
        });
        return;
      }

      const token = `mock-jwt-token-${user.id}-${Date.now()}`;

      sendJSON(res, 200, {
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            username: user.username,
            role: user.role,
            permissions: user.permissions
          },
          token,
          refreshToken: `mock-refresh-token-${user.id}-${Date.now()}`
        }
      });
      return;
    }

    // Get employees
    if (path === '/api/v1/employees' && method === 'GET') {
      const query = parsedUrl.query;
      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedEmployees = mockEmployees.slice(startIndex, endIndex);

      sendJSON(res, 200, {
        success: true,
        message: 'Employees retrieved successfully',
        data: paginatedEmployees,
        pagination: {
          page,
          limit,
          total: mockEmployees.length,
          pages: Math.ceil(mockEmployees.length / limit)
        }
      });
      return;
    }

    // Get employee by ID
    if (path.startsWith('/api/v1/employees/') && method === 'GET') {
      const id = path.split('/').pop();
      const employee = mockEmployees.find(emp => emp.id === id);

      if (!employee) {
        sendJSON(res, 404, {
          success: false,
          message: 'Employee not found'
        });
        return;
      }

      sendJSON(res, 200, {
        success: true,
        message: 'Employee retrieved successfully',
        data: employee
      });
      return;
    }

    // Create employee
    if (path === '/api/v1/employees' && method === 'POST') {
      const body = await parseBody(req);
      const newEmployee = {
        id: (mockEmployees.length + 1).toString(),
        ...body,
        status: 'ACTIVE'
      };

      mockEmployees.push(newEmployee);

      sendJSON(res, 201, {
        success: true,
        message: 'Employee created successfully',
        data: newEmployee
      });
      return;
    }

    // Get statistics
    if (path === '/api/v1/employees/statistics' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        message: 'Statistics retrieved successfully',
        data: {
          totalEmployees: mockEmployees.length,
          activeEmployees: mockEmployees.filter(emp => emp.status === 'ACTIVE').length,
          inactiveEmployees: mockEmployees.filter(emp => emp.status === 'INACTIVE').length,
          byUnit: {
            'الوحدة الأولى': 1,
            'الوحدة الثانية': 1
          },
          byRank: {
            'رقيب أول': 1,
            'عريف': 1
          }
        }
      });
      return;
    }

    // Get attendance
    if (path === '/api/v1/attendance' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        message: 'Attendance records retrieved successfully',
        data: [
          {
            id: '1',
            employeeId: '1',
            date: new Date().toISOString().split('T')[0],
            checkIn: '08:00:00',
            checkOut: '16:00:00',
            workingHours: 8,
            status: 'PRESENT'
          }
        ]
      });
      return;
    }

    // Get leaves
    if (path === '/api/v1/leaves' && method === 'GET') {
      sendJSON(res, 200, {
        success: true,
        message: 'Leave requests retrieved successfully',
        data: [
          {
            id: '1',
            employeeId: '1',
            type: 'ANNUAL',
            startDate: '2024-01-15',
            endDate: '2024-01-20',
            totalDays: 5,
            status: 'APPROVED',
            reason: 'إجازة سنوية'
          }
        ]
      });
      return;
    }

    // Generate reports
    if (path === '/api/v1/reports/generate' && method === 'POST') {
      const body = await parseBody(req);
      const { type, format } = body;

      sendJSON(res, 200, {
        success: true,
        message: `${format} report generated successfully`,
        data: {
          reportId: `report-${Date.now()}`,
          type,
          format,
          generatedAt: new Date().toISOString(),
          downloadUrl: `/api/v1/reports/download/report-${Date.now()}`
        }
      });
      return;
    }

    // 404 handler
    sendJSON(res, 404, {
      success: false,
      message: 'Route not found'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`🚀 Employee Management System Test Server is running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`👤 Login endpoint: POST http://localhost:${PORT}/api/v1/auth/login`);
  console.log(`👥 Employees endpoint: GET http://localhost:${PORT}/api/v1/employees`);
  console.log(`📈 Statistics endpoint: GET http://localhost:${PORT}/api/v1/employees/statistics`);
  console.log('');
  console.log('Test credentials:');
  console.log('Username: admin');
  console.log('Password: admin123');
});

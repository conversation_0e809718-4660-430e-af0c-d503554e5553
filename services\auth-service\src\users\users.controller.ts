import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { 
  CreateUserDto, 
  UpdateUserDto, 
  ChangePasswordDto,
  Permission 
} from '@shared/types/user.types';
import { AuthGuard } from '@nestjs/passport';
import { RequirePermissions } from '@shared/middleware/auth.middleware';
import { AuthenticatedRequest } from '@shared/middleware/auth.middleware';

@Controller('users')
@UseGuards(AuthGuard('jwt'))
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @RequirePermissions(Permission.CREATE_USERS)
  async create(@Body() createUserDto: CreateUserDto) {
    return {
      success: true,
      message: 'User created successfully',
      data: await this.usersService.create(createUserDto)
    };
  }

  @Get()
  @RequirePermissions(Permission.READ_USERS)
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('role') role?: string,
    @Query('isActive') isActive?: string,
    @Query('search') search?: string,
    @Query('department') department?: string,
  ) {
    const filters: any = {};
    
    if (role) filters.role = role;
    if (isActive !== undefined) filters.isActive = isActive === 'true';
    if (search) filters.search = search;
    if (department) filters.department = department;

    const result = await this.usersService.findAll(
      parseInt(page),
      parseInt(limit),
      filters
    );

    return {
      success: true,
      message: 'Users retrieved successfully',
      data: result.users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: result.total,
        pages: Math.ceil(result.total / parseInt(limit))
      }
    };
  }

  @Get('profile')
  async getProfile(@Req() req: AuthenticatedRequest) {
    return {
      success: true,
      message: 'Profile retrieved successfully',
      data: await this.usersService.findOne(req.user._id)
    };
  }

  @Get(':id')
  @RequirePermissions(Permission.READ_USERS)
  async findOne(@Param('id') id: string) {
    return {
      success: true,
      message: 'User retrieved successfully',
      data: await this.usersService.findOne(id)
    };
  }

  @Patch('profile')
  async updateProfile(
    @Req() req: AuthenticatedRequest,
    @Body() updateUserDto: UpdateUserDto
  ) {
    // Users can only update their own profile (limited fields)
    const allowedFields = ['profile'];
    const filteredUpdate: any = {};
    
    if (updateUserDto.profile) {
      filteredUpdate.profile = updateUserDto.profile;
    }

    return {
      success: true,
      message: 'Profile updated successfully',
      data: await this.usersService.update(req.user._id, filteredUpdate)
    };
  }

  @Patch(':id')
  @RequirePermissions(Permission.UPDATE_USERS)
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return {
      success: true,
      message: 'User updated successfully',
      data: await this.usersService.update(id, updateUserDto)
    };
  }

  @Delete(':id')
  @RequirePermissions(Permission.DELETE_USERS)
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.usersService.remove(id);
    return {
      success: true,
      message: 'User deleted successfully'
    };
  }

  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Req() req: AuthenticatedRequest,
    @Body() changePasswordDto: ChangePasswordDto
  ) {
    await this.usersService.changePassword(req.user._id, changePasswordDto);
    return {
      success: true,
      message: 'Password changed successfully'
    };
  }

  @Post(':id/toggle-status')
  @RequirePermissions(Permission.UPDATE_USERS)
  async toggleStatus(@Param('id') id: string) {
    const user = await this.usersService.findOne(id);
    const updatedUser = await this.usersService.update(id, { 
      isActive: !user.isActive 
    });

    return {
      success: true,
      message: `User ${updatedUser.isActive ? 'activated' : 'deactivated'} successfully`,
      data: updatedUser
    };
  }

  @Post(':id/reset-password')
  @RequirePermissions(Permission.UPDATE_USERS)
  async adminResetPassword(@Param('id') id: string) {
    // Generate temporary password
    const tempPassword = Math.random().toString(36).slice(-8);
    
    await this.usersService.update(id, { 
      password: tempPassword 
    } as any);

    return {
      success: true,
      message: 'Password reset successfully',
      data: { temporaryPassword: tempPassword }
    };
  }
}

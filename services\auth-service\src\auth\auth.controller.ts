import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  Get,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { 
  LoginDto, 
  RefreshTokenDto,
  ForgotPasswordDto,
  ResetPasswordDto
} from '@shared/types/user.types';
import { AuthenticatedRequest } from '@shared/middleware/auth.middleware';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto) {
    const result = await this.authService.login(loginDto);
    
    return {
      success: true,
      message: 'Login successful',
      data: result
    };
  }

  @Post('logout')
  @UseGuards(AuthGuard('jwt'))
  @HttpCode(HttpStatus.OK)
  async logout(@Req() req: AuthenticatedRequest) {
    await this.authService.logout(req.user._id);
    
    return {
      success: true,
      message: 'Logout successful'
    };
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    const result = await this.authService.refreshToken(refreshTokenDto);
    
    return {
      success: true,
      message: 'Token refreshed successfully',
      data: result
    };
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    await this.authService.forgotPassword(forgotPasswordDto);
    
    return {
      success: true,
      message: 'Password reset instructions sent to your email'
    };
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    await this.authService.resetPassword(resetPasswordDto);
    
    return {
      success: true,
      message: 'Password reset successfully'
    };
  }

  @Get('me')
  @UseGuards(AuthGuard('jwt'))
  async getProfile(@Req() req: AuthenticatedRequest) {
    return {
      success: true,
      message: 'Profile retrieved successfully',
      data: req.user
    };
  }

  @Get('permissions')
  @UseGuards(AuthGuard('jwt'))
  async getPermissions(@Req() req: AuthenticatedRequest) {
    return {
      success: true,
      message: 'Permissions retrieved successfully',
      data: {
        role: req.user.role,
        permissions: req.user.permissions
      }
    };
  }

  @Post('validate-token')
  @UseGuards(AuthGuard('jwt'))
  @HttpCode(HttpStatus.OK)
  async validateToken(@Req() req: AuthenticatedRequest) {
    return {
      success: true,
      message: 'Token is valid',
      data: {
        valid: true,
        user: req.user
      }
    };
  }
}

export interface User {
  _id?: string;
  username: string;
  email: string;
  password: string;
  role: UserRole;
  permissions: Permission[];
  profile: {
    fullName: string;
    phone?: string;
    department?: string;
    position?: string;
    avatar?: string;
  };
  isActive: boolean;
  lastLogin?: Date;
  loginAttempts: number;
  lockUntil?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDto {
  username: string;
  email: string;
  password: string;
  role: UserRole;
  permissions?: Permission[];
  profile: {
    fullName: string;
    phone?: string;
    department?: string;
    position?: string;
  };
}

export interface UpdateUserDto {
  username?: string;
  email?: string;
  role?: UserRole;
  permissions?: Permission[];
  profile?: Partial<User['profile']>;
  isActive?: boolean;
}

export interface LoginDto {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password'>;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenDto {
  refreshToken: string;
}

export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface ResetPasswordDto {
  token: string;
  newPassword: string;
}

export interface ForgotPasswordDto {
  email: string;
}

// User Roles
export enum UserRole {
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  HR_MANAGER = 'HR_MANAGER',
  HR_OFFICER = 'HR_OFFICER',
  UNIT_COMMANDER = 'UNIT_COMMANDER',
  SUPERVISOR = 'SUPERVISOR',
  EMPLOYEE = 'EMPLOYEE',
  VIEWER = 'VIEWER'
}

// Permissions
export enum Permission {
  // Employee Management
  READ_EMPLOYEES = 'READ_EMPLOYEES',
  CREATE_EMPLOYEES = 'CREATE_EMPLOYEES',
  UPDATE_EMPLOYEES = 'UPDATE_EMPLOYEES',
  DELETE_EMPLOYEES = 'DELETE_EMPLOYEES',
  
  // User Management
  READ_USERS = 'READ_USERS',
  CREATE_USERS = 'CREATE_USERS',
  UPDATE_USERS = 'UPDATE_USERS',
  DELETE_USERS = 'DELETE_USERS',
  MANAGE_PERMISSIONS = 'MANAGE_PERMISSIONS',
  
  // Reports
  READ_REPORTS = 'READ_REPORTS',
  CREATE_REPORTS = 'CREATE_REPORTS',
  EXPORT_REPORTS = 'EXPORT_REPORTS',
  
  // Attendance
  READ_ATTENDANCE = 'READ_ATTENDANCE',
  MANAGE_ATTENDANCE = 'MANAGE_ATTENDANCE',
  
  // Leaves
  READ_LEAVES = 'READ_LEAVES',
  APPROVE_LEAVES = 'APPROVE_LEAVES',
  MANAGE_LEAVES = 'MANAGE_LEAVES',
  
  // Punishments & Rewards
  READ_PUNISHMENTS = 'READ_PUNISHMENTS',
  MANAGE_PUNISHMENTS = 'MANAGE_PUNISHMENTS',
  READ_REWARDS = 'READ_REWARDS',
  MANAGE_REWARDS = 'MANAGE_REWARDS',
  
  // Files
  UPLOAD_FILES = 'UPLOAD_FILES',
  DELETE_FILES = 'DELETE_FILES',
  
  // Barcode
  GENERATE_BARCODES = 'GENERATE_BARCODES',
  SCAN_BARCODES = 'SCAN_BARCODES',
  
  // System
  MANAGE_SYSTEM = 'MANAGE_SYSTEM',
  VIEW_LOGS = 'VIEW_LOGS',
  BACKUP_DATA = 'BACKUP_DATA',
  
  // Special Permissions
  READ_ALL = 'READ_ALL',
  WRITE_ALL = 'WRITE_ALL',
  DELETE_ALL = 'DELETE_ALL'
}

// Role-Permission Mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.SYSTEM_ADMIN]: [
    Permission.READ_ALL,
    Permission.WRITE_ALL,
    Permission.DELETE_ALL,
    Permission.MANAGE_SYSTEM,
    Permission.VIEW_LOGS,
    Permission.BACKUP_DATA
  ],
  [UserRole.HR_MANAGER]: [
    Permission.READ_EMPLOYEES,
    Permission.CREATE_EMPLOYEES,
    Permission.UPDATE_EMPLOYEES,
    Permission.DELETE_EMPLOYEES,
    Permission.READ_USERS,
    Permission.CREATE_USERS,
    Permission.UPDATE_USERS,
    Permission.READ_REPORTS,
    Permission.CREATE_REPORTS,
    Permission.EXPORT_REPORTS,
    Permission.READ_ATTENDANCE,
    Permission.MANAGE_ATTENDANCE,
    Permission.READ_LEAVES,
    Permission.APPROVE_LEAVES,
    Permission.MANAGE_LEAVES,
    Permission.READ_PUNISHMENTS,
    Permission.MANAGE_PUNISHMENTS,
    Permission.READ_REWARDS,
    Permission.MANAGE_REWARDS,
    Permission.UPLOAD_FILES,
    Permission.DELETE_FILES,
    Permission.GENERATE_BARCODES,
    Permission.SCAN_BARCODES
  ],
  [UserRole.HR_OFFICER]: [
    Permission.READ_EMPLOYEES,
    Permission.CREATE_EMPLOYEES,
    Permission.UPDATE_EMPLOYEES,
    Permission.READ_REPORTS,
    Permission.CREATE_REPORTS,
    Permission.EXPORT_REPORTS,
    Permission.READ_ATTENDANCE,
    Permission.MANAGE_ATTENDANCE,
    Permission.READ_LEAVES,
    Permission.MANAGE_LEAVES,
    Permission.READ_PUNISHMENTS,
    Permission.READ_REWARDS,
    Permission.UPLOAD_FILES,
    Permission.GENERATE_BARCODES,
    Permission.SCAN_BARCODES
  ],
  [UserRole.UNIT_COMMANDER]: [
    Permission.READ_EMPLOYEES,
    Permission.UPDATE_EMPLOYEES,
    Permission.READ_REPORTS,
    Permission.CREATE_REPORTS,
    Permission.READ_ATTENDANCE,
    Permission.READ_LEAVES,
    Permission.APPROVE_LEAVES,
    Permission.READ_PUNISHMENTS,
    Permission.MANAGE_PUNISHMENTS,
    Permission.READ_REWARDS,
    Permission.MANAGE_REWARDS,
    Permission.SCAN_BARCODES
  ],
  [UserRole.SUPERVISOR]: [
    Permission.READ_EMPLOYEES,
    Permission.READ_REPORTS,
    Permission.READ_ATTENDANCE,
    Permission.READ_LEAVES,
    Permission.SCAN_BARCODES
  ],
  [UserRole.EMPLOYEE]: [
    Permission.READ_EMPLOYEES,
    Permission.READ_ATTENDANCE,
    Permission.READ_LEAVES
  ],
  [UserRole.VIEWER]: [
    Permission.READ_EMPLOYEES,
    Permission.READ_REPORTS,
    Permission.READ_ATTENDANCE
  ]
};

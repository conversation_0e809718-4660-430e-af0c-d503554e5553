import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AttendanceService, CreateAttendanceDto, UpdateAttendanceDto } from './attendance.service';
import { AttendanceMethod, Permission } from '@shared/types/employee.types';
import { RequirePermissions } from '@shared/middleware/auth.middleware';
import { AuthenticatedRequest } from '@shared/middleware/auth.middleware';

@Controller('attendance')
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  @Post()
  @RequirePermissions(Permission.MANAGE_ATTENDANCE)
  async create(
    @Body() createAttendanceDto: CreateAttendanceDto,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Attendance record created successfully',
      data: await this.attendanceService.create(createAttendanceDto, req.user._id)
    };
  }

  @Get()
  @RequirePermissions(Permission.READ_ATTENDANCE)
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('employeeId') employeeId?: string,
    @Query('status') status?: string,
    @Query('method') method?: string,
    @Query('isLate') isLate?: string,
    @Query('isEarlyLeave') isEarlyLeave?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('location') location?: string,
  ) {
    const filters: any = {};
    
    if (employeeId) filters.employeeId = employeeId;
    if (status) filters.status = status;
    if (method) filters.method = method;
    if (isLate !== undefined) filters.isLate = isLate === 'true';
    if (isEarlyLeave !== undefined) filters.isEarlyLeave = isEarlyLeave === 'true';
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (location) filters.location = location;

    const result = await this.attendanceService.findAll(
      parseInt(page),
      parseInt(limit),
      filters
    );

    return {
      success: true,
      message: 'Attendance records retrieved successfully',
      data: result.attendance,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: result.total,
        pages: Math.ceil(result.total / parseInt(limit))
      }
    };
  }

  @Get('employee/:employeeId')
  @RequirePermissions(Permission.READ_ATTENDANCE)
  async findByEmployee(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    return {
      success: true,
      message: 'Employee attendance records retrieved successfully',
      data: await this.attendanceService.findByEmployee(employeeId, start, end)
    };
  }

  @Get('employee/:employeeId/summary')
  @RequirePermissions(Permission.READ_ATTENDANCE)
  async getAttendanceSummary(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return {
      success: true,
      message: 'Attendance summary retrieved successfully',
      data: await this.attendanceService.getAttendanceSummary(
        employeeId,
        new Date(startDate),
        new Date(endDate)
      )
    };
  }

  @Get('daily-report')
  @RequirePermissions(Permission.READ_ATTENDANCE)
  async getDailyReport(@Query('date') date: string) {
    const reportDate = date ? new Date(date) : new Date();
    
    return {
      success: true,
      message: 'Daily attendance report retrieved successfully',
      data: await this.attendanceService.getDailyAttendanceReport(reportDate)
    };
  }

  @Get(':id')
  @RequirePermissions(Permission.READ_ATTENDANCE)
  async findOne(@Param('id') id: string) {
    return {
      success: true,
      message: 'Attendance record retrieved successfully',
      data: await this.attendanceService.findOne(id)
    };
  }

  @Patch(':id')
  @RequirePermissions(Permission.MANAGE_ATTENDANCE)
  async update(@Param('id') id: string, @Body() updateAttendanceDto: UpdateAttendanceDto) {
    return {
      success: true,
      message: 'Attendance record updated successfully',
      data: await this.attendanceService.update(id, updateAttendanceDto)
    };
  }

  @Delete(':id')
  @RequirePermissions(Permission.MANAGE_ATTENDANCE)
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.attendanceService.remove(id);
    return {
      success: true,
      message: 'Attendance record deleted successfully'
    };
  }

  @Post('check-in')
  @RequirePermissions(Permission.SCAN_BARCODES)
  async checkIn(
    @Body() body: { 
      employeeId: string; 
      method: AttendanceMethod; 
      location?: string; 
    },
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Check-in recorded successfully',
      data: await this.attendanceService.checkIn(
        body.employeeId,
        body.method,
        body.location,
        req.user._id
      )
    };
  }

  @Post('check-out')
  @RequirePermissions(Permission.SCAN_BARCODES)
  async checkOut(
    @Body() body: { 
      employeeId: string; 
      method: AttendanceMethod; 
      location?: string; 
    }
  ) {
    return {
      success: true,
      message: 'Check-out recorded successfully',
      data: await this.attendanceService.checkOut(
        body.employeeId,
        body.method,
        body.location
      )
    };
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { EmployeesService } from './employees.service';
import { 
  CreateEmployeeDto, 
  UpdateEmployeeDto,
  Permission 
} from '@shared/types/employee.types';
import { RequirePermissions } from '@shared/middleware/auth.middleware';
import { AuthenticatedRequest } from '@shared/middleware/auth.middleware';

@Controller('employees')
export class EmployeesController {
  constructor(private readonly employeesService: EmployeesService) {}

  @Post()
  @RequirePermissions(Permission.CREATE_EMPLOYEES)
  async create(
    @Body() createEmployeeDto: CreateEmployeeDto,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Employee created successfully',
      data: await this.employeesService.create(createEmployeeDto, req.user._id)
    };
  }

  @Get()
  @RequirePermissions(Permission.READ_EMPLOYEES)
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('unit') unit?: string,
    @Query('rank') rank?: string,
    @Query('status') status?: string,
    @Query('isActive') isActive?: string,
    @Query('search') search?: string,
    @Query('appointmentDateFrom') appointmentDateFrom?: string,
    @Query('appointmentDateTo') appointmentDateTo?: string,
  ) {
    const filters: any = {};
    
    if (unit) filters.unit = unit;
    if (rank) filters.rank = rank;
    if (status) filters.status = status;
    if (isActive !== undefined) filters.isActive = isActive === 'true';
    if (search) filters.search = search;
    if (appointmentDateFrom) filters.appointmentDateFrom = appointmentDateFrom;
    if (appointmentDateTo) filters.appointmentDateTo = appointmentDateTo;

    const result = await this.employeesService.findAll(
      parseInt(page),
      parseInt(limit),
      filters
    );

    return {
      success: true,
      message: 'Employees retrieved successfully',
      data: result.employees,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: result.total,
        pages: Math.ceil(result.total / parseInt(limit))
      }
    };
  }

  @Get('statistics')
  @RequirePermissions(Permission.READ_EMPLOYEES)
  async getStatistics() {
    return {
      success: true,
      message: 'Statistics retrieved successfully',
      data: await this.employeesService.getStatistics()
    };
  }

  @Get('military/:militaryNumber')
  @RequirePermissions(Permission.READ_EMPLOYEES)
  async findByMilitaryNumber(@Param('militaryNumber') militaryNumber: string) {
    return {
      success: true,
      message: 'Employee retrieved successfully',
      data: await this.employeesService.findByMilitaryNumber(militaryNumber)
    };
  }

  @Get('barcode/:barcode')
  @RequirePermissions(Permission.SCAN_BARCODES)
  async findByBarcode(@Param('barcode') barcode: string) {
    return {
      success: true,
      message: 'Employee retrieved successfully',
      data: await this.employeesService.findByBarcode(barcode)
    };
  }

  @Get(':id')
  @RequirePermissions(Permission.READ_EMPLOYEES)
  async findOne(@Param('id') id: string) {
    return {
      success: true,
      message: 'Employee retrieved successfully',
      data: await this.employeesService.findOne(id)
    };
  }

  @Patch(':id')
  @RequirePermissions(Permission.UPDATE_EMPLOYEES)
  async update(
    @Param('id') id: string, 
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Employee updated successfully',
      data: await this.employeesService.update(id, updateEmployeeDto, req.user._id)
    };
  }

  @Delete(':id')
  @RequirePermissions(Permission.DELETE_EMPLOYEES)
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.employeesService.remove(id);
    return {
      success: true,
      message: 'Employee deleted successfully'
    };
  }

  @Post(':id/toggle-status')
  @RequirePermissions(Permission.UPDATE_EMPLOYEES)
  async toggleStatus(
    @Param('id') id: string,
    @Req() req: AuthenticatedRequest
  ) {
    const updatedEmployee = await this.employeesService.toggleStatus(id, req.user._id);
    
    return {
      success: true,
      message: `Employee ${updatedEmployee.systemInfo.isActive ? 'activated' : 'deactivated'} successfully`,
      data: updatedEmployee
    };
  }

  @Post(':id/photo')
  @RequirePermissions(Permission.UPDATE_EMPLOYEES)
  @UseInterceptors(FileInterceptor('photo', {
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
    fileFilter: (req, file, cb) => {
      if (file.mimetype.match(/\/(jpg|jpeg|png)$/)) {
        cb(null, true);
      } else {
        cb(new Error('Only image files are allowed!'), false);
      }
    }
  }))
  async uploadPhoto(
    @Param('id') id: string,
    @UploadedFile() file: any
  ) {
    return {
      success: true,
      message: 'Photo uploaded successfully',
      data: await this.employeesService.uploadPhoto(id, file)
    };
  }

  @Post(':id/punishments')
  @RequirePermissions(Permission.MANAGE_PUNISHMENTS)
  async addPunishment(
    @Param('id') id: string,
    @Body() punishment: any,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Punishment added successfully',
      data: await this.employeesService.addPunishment(id, punishment, req.user._id)
    };
  }

  @Post(':id/rewards')
  @RequirePermissions(Permission.MANAGE_REWARDS)
  async addReward(
    @Param('id') id: string,
    @Body() reward: any,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Reward added successfully',
      data: await this.employeesService.addReward(id, reward, req.user._id)
    };
  }

  @Patch(':id/leave-balance')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async updateLeaveBalance(
    @Param('id') id: string,
    @Body('balance') balance: number,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Leave balance updated successfully',
      data: await this.employeesService.updateLeaveBalance(id, balance, req.user._id)
    };
  }
}

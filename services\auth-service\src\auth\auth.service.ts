import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

import { UsersService } from '../users/users.service';
import { 
  LoginDto, 
  LoginResponse, 
  RefreshTokenDto,
  ForgotPasswordDto,
  ResetPasswordDto
} from '@shared/types/user.types';
import { UserDocument } from '../users/schemas/user.schema';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async login(loginDto: LoginDto): Promise<LoginResponse> {
    const user = await this.validateUser(loginDto.username, loginDto.password);
    
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    if (user.isLocked) {
      throw new UnauthorizedException('Account is temporarily locked due to too many failed login attempts');
    }

    // Reset login attempts on successful login
    await user.resetLoginAttempts();
    
    // Update last login
    await this.usersService.updateLastLogin(user._id);

    // Generate tokens
    const tokens = await this.generateTokens(user);
    
    // Save refresh token
    await this.usersService.updateRefreshToken(user._id, tokens.refreshToken);

    return {
      user: this.sanitizeUser(user),
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: this.getTokenExpirationTime()
    };
  }

  async logout(userId: string): Promise<void> {
    await this.usersService.updateRefreshToken(userId, null);
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<LoginResponse> {
    const user = await this.usersService.findByRefreshToken(refreshTokenDto.refreshToken);
    
    if (!user) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    // Generate new tokens
    const tokens = await this.generateTokens(user);
    
    // Update refresh token
    await this.usersService.updateRefreshToken(user._id, tokens.refreshToken);

    return {
      user: this.sanitizeUser(user),
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: this.getTokenExpirationTime()
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    const resetToken = await this.usersService.generatePasswordResetToken(forgotPasswordDto.email);
    
    // Here you would typically send an email with the reset token
    // For now, we'll just log it (in production, use a proper email service)
    console.log(`Password reset token for ${forgotPasswordDto.email}: ${resetToken}`);
    
    // In a real application, you would send an email like this:
    // await this.emailService.sendPasswordResetEmail(forgotPasswordDto.email, resetToken);
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    await this.usersService.resetPassword(resetPasswordDto.token, resetPasswordDto.newPassword);
  }

  async validateUser(username: string, password: string): Promise<UserDocument | null> {
    const user = await this.usersService.findByUsername(username);
    
    if (!user) {
      return null;
    }

    // Check if account is locked
    if (user.isLocked) {
      return null;
    }

    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      // Increment login attempts
      await user.incLoginAttempts();
      return null;
    }

    return user;
  }

  async validateUserById(userId: string): Promise<UserDocument | null> {
    try {
      const user = await this.usersService.findOne(userId);
      return user as any; // Type assertion for compatibility
    } catch (error) {
      return null;
    }
  }

  private async generateTokens(user: UserDocument): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = {
      sub: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      profile: user.profile,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        expiresIn: this.configService.get('JWT_EXPIRES_IN', '24h'),
      }),
      this.jwtService.signAsync(payload, {
        expiresIn: '7d', // Refresh token expires in 7 days
      }),
    ]);

    return { accessToken, refreshToken };
  }

  private getTokenExpirationTime(): number {
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '24h');
    
    // Convert to seconds
    if (expiresIn.endsWith('h')) {
      return parseInt(expiresIn) * 60 * 60;
    } else if (expiresIn.endsWith('d')) {
      return parseInt(expiresIn) * 24 * 60 * 60;
    } else if (expiresIn.endsWith('m')) {
      return parseInt(expiresIn) * 60;
    } else {
      return parseInt(expiresIn); // Assume seconds
    }
  }

  private sanitizeUser(user: UserDocument): any {
    const userObj = user.toObject();
    delete userObj.password;
    delete userObj.refreshToken;
    delete userObj.passwordResetToken;
    delete userObj.passwordResetExpires;
    delete userObj.refreshTokenExpires;
    delete userObj.loginAttempts;
    delete userObj.lockUntil;
    return userObj;
  }
}

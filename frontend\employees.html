<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="employees.html" class="nav-link active">
                    <i class="fas fa-users"></i>
                    الموظفين
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-clock"></i>
                    الحضور
                </a>
                <a href="leaves.html" class="nav-link">
                    <i class="fas fa-calendar-alt"></i>
                    الإجازات
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="userName">المدير</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-users"></i>
                    إدارة الموظفين
                </h1>
                <p>إدارة وعرض بيانات الموظفين</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                    <i class="fas fa-plus"></i>
                    إضافة موظف جديد
                </button>
                <button class="btn btn-success" onclick="exportEmployees()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalEmployeesCount">0</h3>
                    <p>إجمالي الموظفين</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-content">
                    <h3 id="activeEmployeesCount">0</h3>
                    <p>الموظفين النشطين</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-user-clock"></i>
                </div>
                <div class="stat-content">
                    <h3 id="inactiveEmployeesCount">0</h3>
                    <p>الموظفين غير النشطين</p>
                </div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stat-content">
                    <h3 id="newEmployeesCount">0</h3>
                    <p>الموظفين الجدد هذا الشهر</p>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="filters-section">
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="employeeSearch" placeholder="البحث عن موظف (الاسم، الرقم العسكري، الرتبة...)">
                    <button class="search-btn" onclick="searchEmployees()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <div class="filter-group">
                <div class="filter-item">
                    <label>الوحدة:</label>
                    <select id="unitFilter" onchange="filterEmployees()">
                        <option value="">جميع الوحدات</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>الرتبة:</label>
                    <select id="rankFilter" onchange="filterEmployees()">
                        <option value="">جميع الرتب</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>الحالة:</label>
                    <select id="statusFilter" onchange="filterEmployees()">
                        <option value="">جميع الحالات</option>
                        <option value="ACTIVE">نشط</option>
                        <option value="INACTIVE">غير نشط</option>
                        <option value="ON_LEAVE">في إجازة</option>
                        <option value="SUSPENDED">موقوف</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>المنصب:</label>
                    <select id="positionFilter" onchange="filterEmployees()">
                        <option value="">جميع المناصب</option>
                    </select>
                </div>
                <button class="btn btn-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>

        <!-- Employees Table -->
        <div class="table-container">
            <div class="table-header">
                <div class="table-title">
                    <h3>قائمة الموظفين</h3>
                    <span class="record-count">عدد السجلات: <span id="recordCount">0</span></span>
                </div>
                <div class="table-actions">
                    <button class="btn btn-sm btn-info" onclick="refreshEmployees()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <div class="view-options">
                        <button class="btn btn-sm" onclick="setTableView('grid')" id="gridViewBtn">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn btn-sm active" onclick="setTableView('table')" id="tableViewBtn">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Table View -->
            <div id="tableView" class="table-view">
                <table class="data-table" id="employeesTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>الصورة</th>
                            <th onclick="sortTable('fullName')" class="sortable">
                                الاسم الكامل
                                <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortTable('militaryNumber')" class="sortable">
                                الرقم العسكري
                                <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortTable('rank')" class="sortable">
                                الرتبة
                                <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortTable('unit')" class="sortable">
                                الوحدة
                                <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortTable('position')" class="sortable">
                                المنصب
                                <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortTable('status')" class="sortable">
                                الحالة
                                <i class="fas fa-sort"></i>
                            </th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        <!-- Employee data will be loaded here -->
                    </tbody>
                </table>
            </div>

            <!-- Grid View -->
            <div id="gridView" class="grid-view" style="display: none;">
                <div id="employeesGrid" class="employees-grid">
                    <!-- Employee cards will be loaded here -->
                </div>
            </div>

            <!-- No Data Message -->
            <div id="noDataMessage" class="no-data-message" style="display: none;">
                <i class="fas fa-users"></i>
                <h3>لا توجد بيانات موظفين</h3>
                <p>لم يتم العثور على أي موظفين يطابقون معايير البحث</p>
                <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                    <i class="fas fa-plus"></i>
                    إضافة موظف جديد
                </button>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span>عرض <span id="showingFrom">1</span> إلى <span id="showingTo">10</span> من <span id="totalRecords">0</span> سجل</span>
            </div>
            <div class="pagination" id="employeesPagination">
                <!-- Pagination will be rendered here -->
            </div>
            <div class="page-size-selector">
                <label>عدد السجلات في الصفحة:</label>
                <select id="pageSizeSelect" onchange="changePageSize()">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/employees.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!isAuthenticated()) {
                redirectToLogin();
                return;
            }
            
            // Load page data
            initializeEmployeesPage();
        });
        
        // Initialize employees page
        async function initializeEmployeesPage() {
            try {
                showLoading(true);
                
                // Load employees data
                await loadEmployeesData();
                
                // Load filter options
                loadFilterOptions();
                
                // Update statistics
                updateEmployeeStatistics();
                
                console.log('Employees page initialized successfully');
            } catch (error) {
                console.error('Error initializing employees page:', error);
                showError('خطأ في تحميل صفحة الموظفين');
            } finally {
                showLoading(false);
            }
        }
    </script>
</body>
</html>

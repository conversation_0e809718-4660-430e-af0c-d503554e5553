# دليل المستخدم - نظام إدارة الموظفين

## 🚀 بدء التشغيل

### 1. تشغيل النظام
```bash
node test-server.js
```

### 2. فتح النظام في المتصفح
افتح الرابط: http://localhost:3000/frontend/login.html

### 3. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📋 الصفحات المتاحة

### 1. لوحة التحكم الرئيسية
- عرض الإحصائيات العامة
- بطاقات الموظفين والحضور
- الوصول السريع للوظائف

### 2. إدارة الموظفين
- عرض قائمة الموظفين
- البحث والتصفية
- إضافة موظف جديد
- تعديل بيانات الموظف

### 3. نموذج الموظف
- إدخال البيانات الشخصية
- رفع صورة الموظف
- تحديد الرتبة والوحدة
- حفظ البيانات

### 4. إدارة الحضور
- تسجيل الحضور والانصراف
- عرض حالة الحضور اليومي
- إحصائيات الحضور
- تسجيل الإجازات

### 5. إدارة الإجازات
- طلب إجازة جديدة
- عرض الإجازات المعلقة
- موافقة/رفض الطلبات
- رصيد الإجازات

### 6. التقارير
- تقرير الموظفين
- تقرير الحضور
- تقرير الإجازات
- الإحصائيات العامة

## 🎨 الميزات البصرية الجديدة

### 1. الإشعارات التفاعلية
- إشعارات النجاح (أخضر)
- إشعارات التحذير (أصفر)
- إشعارات الخطأ (أحمر)
- إشعارات المعلومات (أزرق)

### 2. التأثيرات البصرية
- تأثيرات الرفع عند التمرير
- رسوم متحركة للبطاقات
- تأثيرات الأزرار التفاعلية
- مؤشرات الحالة الملونة

### 3. التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- تخطيط مرن للهواتف والأجهزة اللوحية
- قوائم قابلة للطي

## 🔧 كيفية الاستخدام

### إضافة موظف جديد:
1. اذهب إلى صفحة "الموظفين"
2. اضغط على "إضافة موظف جديد"
3. املأ البيانات المطلوبة
4. ارفع صورة الموظف
5. اضغط "حفظ"

### تسجيل الحضور:
1. اذهب إلى صفحة "الحضور"
2. ابحث عن الموظف
3. اضغط "تسجيل حضور" أو "تسجيل انصراف"
4. سيتم تحديث الحالة تلقائياً

### إنشاء تقرير:
1. اذهب إلى صفحة "التقارير"
2. اختر نوع التقرير
3. حدد التواريخ والمعايير
4. اضغط "إنشاء التقرير"
5. يمكن تصدير التقرير كـ PDF أو Excel

## 🎯 نصائح للاستخدام

### 1. الأداء الأمثل
- استخدم متصفح حديث (Chrome, Firefox, Edge)
- تأكد من اتصال إنترنت مستقر
- أغلق التبويبات غير المستخدمة

### 2. الأمان
- لا تشارك بيانات تسجيل الدخول
- سجل الخروج عند الانتهاء
- استخدم كلمات مرور قوية

### 3. النسخ الاحتياطي
- احفظ البيانات المهمة بانتظام
- صدر التقارير للحفظ الخارجي
- تأكد من عمل النظام بانتظام

## 🐛 حل المشاكل الشائعة

### المشكلة: الصفحة لا تحمل
**الحل:**
1. تأكد من تشغيل الخادم
2. تحقق من الرابط: http://localhost:3000
3. امسح ذاكرة التخزين المؤقت للمتصفح

### المشكلة: الأيقونات لا تظهر
**الحل:**
1. تحقق من اتصال الإنترنت
2. أعد تحميل الصفحة
3. تأكد من عدم حجب Font Awesome

### المشكلة: البيانات لا تحفظ
**الحل:**
1. تحقق من صحة البيانات المدخلة
2. تأكد من الاتصال بالخادم
3. راجع رسائل الخطأ في الإشعارات

## 📞 الدعم التقني

### للمساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف SYSTEM_ANALYSIS.md
3. اختبر النظام على صفحة demo.html

### معلومات النظام:
- **الإصدار:** 1.0.0
- **التقنيات:** Node.js, HTML5, CSS3, JavaScript
- **المتصفحات المدعومة:** Chrome, Firefox, Safari, Edge
- **اللغات:** العربية (RTL)

## 🎉 استمتع بالاستخدام!

النظام جاهز للاستخدام مع جميع الميزات المطلوبة:
- ✅ تصميم جميل ومحسن
- ✅ أيقونات وتأثيرات بصرية
- ✅ سهولة الاستخدام
- ✅ دعم كامل للعربية
- ✅ تصميم متجاوب

نتمنى لك تجربة ممتعة مع النظام!

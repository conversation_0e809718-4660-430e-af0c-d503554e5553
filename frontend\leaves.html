<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإجازات - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .leave-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-card.pending .stat-number {
            color: var(--warning-color);
        }
        
        .stat-card.approved .stat-number {
            color: var(--success-color);
        }
        
        .stat-card.rejected .stat-number {
            color: var(--danger-color);
        }
        
        .stat-card.total .stat-number {
            color: var(--primary-color);
        }
        
        .leave-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .leave-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .leave-status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .leave-status-badge.pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .leave-status-badge.approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .leave-status-badge.rejected {
            background: var(--danger-light);
            color: var(--danger-dark);
        }
        
        .leave-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .leave-type-badge.annual {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .leave-type-badge.sick {
            background: var(--danger-light);
            color: var(--danger-dark);
        }
        
        .leave-type-badge.emergency {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .leave-type-badge.maternity {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .leave-type-badge.paternity {
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .leave-duration {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .leave-balance-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .balance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .balance-item {
            text-align: center;
        }
        
        .balance-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .balance-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .approval-actions {
            display: flex;
            gap: 5px;
        }
        
        .approval-actions .btn {
            padding: 5px 10px;
            font-size: 0.875rem;
        }
        
        @media (max-width: 768px) {
            .leave-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .leave-actions,
            .leave-filters,
            .quick-actions {
                flex-direction: column;
            }
            
            .balance-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    الموظفين
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-clock"></i>
                    الحضور
                </a>
                <a href="leaves.html" class="nav-link active">
                    <i class="fas fa-calendar-alt"></i>
                    الإجازات
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="userName">المدير</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-calendar-alt"></i>
                    إدارة الإجازات
                </h1>
                <p>إدارة طلبات الإجازات وأرصدة الموظفين</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="showNewLeaveModal()">
                    <i class="fas fa-plus"></i>
                    طلب إجازة جديد
                </button>
                <button class="btn btn-success" onclick="exportLeaves()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>

        <!-- Leave Balance Card -->
        <div class="leave-balance-card">
            <h3>
                <i class="fas fa-calendar-check"></i>
                أرصدة الإجازات الإجمالية
            </h3>
            <div class="balance-grid">
                <div class="balance-item">
                    <div class="balance-number" id="totalAnnualBalance">0</div>
                    <div class="balance-label">إجازة سنوية</div>
                </div>
                <div class="balance-item">
                    <div class="balance-number" id="totalSickBalance">0</div>
                    <div class="balance-label">إجازة مرضية</div>
                </div>
                <div class="balance-item">
                    <div class="balance-number" id="totalEmergencyBalance">0</div>
                    <div class="balance-label">إجازة طارئة</div>
                </div>
                <div class="balance-item">
                    <div class="balance-number" id="totalUsedLeaves">0</div>
                    <div class="balance-label">إجازات مستخدمة</div>
                </div>
            </div>
        </div>

        <!-- Leave Statistics -->
        <div class="leave-stats">
            <div class="stat-card pending">
                <div class="stat-number" id="pendingLeavesCount">0</div>
                <div class="stat-label">طلبات معلقة</div>
            </div>
            <div class="stat-card approved">
                <div class="stat-number" id="approvedLeavesCount">0</div>
                <div class="stat-label">طلبات موافق عليها</div>
            </div>
            <div class="stat-card rejected">
                <div class="stat-number" id="rejectedLeavesCount">0</div>
                <div class="stat-label">طلبات مرفوضة</div>
            </div>
            <div class="stat-card total">
                <div class="stat-number" id="totalLeavesCount">0</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <button class="btn btn-warning" onclick="showPendingLeaves()">
                <i class="fas fa-clock"></i>
                الطلبات المعلقة (<span id="pendingCount">0</span>)
            </button>
            <button class="btn btn-info" onclick="showLeaveCalendar()">
                <i class="fas fa-calendar"></i>
                تقويم الإجازات
            </button>
            <button class="btn btn-secondary" onclick="showLeaveBalances()">
                <i class="fas fa-chart-pie"></i>
                أرصدة الموظفين
            </button>
        </div>

        <!-- Leave Filters -->
        <div class="leave-filters">
            <div class="filter-group">
                <label>من تاريخ:</label>
                <input type="date" id="fromDate" onchange="filterLeaves()">
            </div>
            <div class="filter-group">
                <label>إلى تاريخ:</label>
                <input type="date" id="toDate" onchange="filterLeaves()">
            </div>
            <div class="filter-group">
                <label>نوع الإجازة:</label>
                <select id="leaveTypeFilter" onchange="filterLeaves()">
                    <option value="">جميع الأنواع</option>
                    <option value="ANNUAL">إجازة سنوية</option>
                    <option value="SICK">إجازة مرضية</option>
                    <option value="EMERGENCY">إجازة طارئة</option>
                    <option value="MATERNITY">إجازة أمومة</option>
                    <option value="PATERNITY">إجازة أبوة</option>
                </select>
            </div>
            <div class="filter-group">
                <label>الحالة:</label>
                <select id="statusFilter" onchange="filterLeaves()">
                    <option value="">جميع الحالات</option>
                    <option value="PENDING">معلق</option>
                    <option value="APPROVED">موافق عليه</option>
                    <option value="REJECTED">مرفوض</option>
                </select>
            </div>
            <div class="filter-group">
                <label>الموظف:</label>
                <input type="text" id="employeeFilter" placeholder="اسم الموظف..." onkeyup="filterLeaves()">
            </div>
            <button class="btn btn-secondary" onclick="clearLeaveFilters()">
                <i class="fas fa-times"></i>
                مسح الفلاتر
            </button>
        </div>

        <!-- Leaves Table -->
        <div class="table-container">
            <div class="table-header">
                <div class="table-title">
                    <h3>طلبات الإجازات</h3>
                    <span class="record-count">عدد السجلات: <span id="leavesRecordCount">0</span></span>
                </div>
                <div class="table-actions">
                    <button class="btn btn-sm btn-info" onclick="refreshLeaves()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <table class="data-table" id="leavesTable">
                <thead>
                    <tr>
                        <th onclick="sortLeavesTable('employeeName')" class="sortable">
                            اسم الموظف
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortLeavesTable('leaveType')" class="sortable">
                            نوع الإجازة
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortLeavesTable('startDate')" class="sortable">
                            تاريخ البداية
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortLeavesTable('endDate')" class="sortable">
                            تاريخ النهاية
                            <i class="fas fa-sort"></i>
                        </th>
                        <th>المدة</th>
                        <th onclick="sortLeavesTable('status')" class="sortable">
                            الحالة
                            <i class="fas fa-sort"></i>
                        </th>
                        <th onclick="sortLeavesTable('requestDate')" class="sortable">
                            تاريخ الطلب
                            <i class="fas fa-sort"></i>
                        </th>
                        <th>السبب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="leavesTableBody">
                    <!-- Leave data will be loaded here -->
                </tbody>
            </table>

            <!-- No Data Message -->
            <div id="noLeavesData" class="no-data-message" style="display: none;">
                <i class="fas fa-calendar-alt"></i>
                <h3>لا توجد طلبات إجازات</h3>
                <p>لم يتم العثور على أي طلبات إجازات للفترة المحددة</p>
                <button class="btn btn-primary" onclick="showNewLeaveModal()">
                    <i class="fas fa-plus"></i>
                    إضافة طلب إجازة جديد
                </button>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span>عرض <span id="leavesShowingFrom">1</span> إلى <span id="leavesShowingTo">10</span> من <span id="leavesTotalRecords">0</span> سجل</span>
            </div>
            <div class="pagination" id="leavesPagination">
                <!-- Pagination will be rendered here -->
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/leaves.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!isAuthenticated()) {
                redirectToLogin();
                return;
            }
            
            // Initialize leaves page
            initializeLeavesPage();
        });
        
        // Initialize leaves page
        async function initializeLeavesPage() {
            try {
                showLoading(true);
                
                // Load leaves data
                await loadLeavesData();
                
                // Load filter options
                loadLeaveFilterOptions();
                
                // Update statistics
                updateLeaveStatistics();
                
                // Update leave balances
                updateLeaveBalances();
                
                console.log('Leaves page initialized successfully');
            } catch (error) {
                console.error('Error initializing leaves page:', error);
                showError('خطأ في تحميل صفحة الإجازات');
            } finally {
                showLoading(false);
            }
        }
        
        // Load leave filter options
        function loadLeaveFilterOptions() {
            // Set default dates (current month)
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            document.getElementById('fromDate').value = firstDay.toISOString().split('T')[0];
            document.getElementById('toDate').value = lastDay.toISOString().split('T')[0];
        }
    </script>
</body>
</html>

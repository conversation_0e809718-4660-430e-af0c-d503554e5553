import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as ExcelJS from 'exceljs';
import * as puppeteer from 'puppeteer';
import { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell } from 'docx';
import * as Handlebars from 'handlebars';
import * as moment from 'moment';
import 'moment/locale/ar';
import * as fs from 'fs/promises';
import * as path from 'path';

import { ReportType, ReportFormat } from '@shared/types/report.types';

export interface GenerateReportDto {
  type: ReportType;
  format: ReportFormat;
  filters?: any;
  template?: string;
  title?: string;
  includeCharts?: boolean;
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
}

@Injectable()
export class ReportsService {
  constructor() {
    // Set Arabic locale for moment
    moment.locale('ar');
    
    // Register Handlebars helpers
    this.registerHandlebarsHelpers();
  }

  async generateReport(reportDto: GenerateReportDto, generatedBy: string): Promise<Buffer> {
    const data = await this.fetchReportData(reportDto.type, reportDto.filters);
    
    switch (reportDto.format) {
      case ReportFormat.PDF:
        return this.generatePDFReport(data, reportDto);
      case ReportFormat.EXCEL:
        return this.generateExcelReport(data, reportDto);
      case ReportFormat.WORD:
        return this.generateWordReport(data, reportDto);
      default:
        throw new BadRequestException('Unsupported report format');
    }
  }

  private async fetchReportData(type: ReportType, filters?: any): Promise<any> {
    // This would typically fetch data from the employee service
    // For now, we'll return mock data structure
    
    switch (type) {
      case ReportType.EMPLOYEE_LIST:
        return this.fetchEmployeeListData(filters);
      case ReportType.ATTENDANCE_SUMMARY:
        return this.fetchAttendanceSummaryData(filters);
      case ReportType.LEAVE_REPORT:
        return this.fetchLeaveReportData(filters);
      case ReportType.PAYROLL_REPORT:
        return this.fetchPayrollReportData(filters);
      case ReportType.PERFORMANCE_REPORT:
        return this.fetchPerformanceReportData(filters);
      case ReportType.DISCIPLINARY_REPORT:
        return this.fetchDisciplinaryReportData(filters);
      default:
        throw new BadRequestException('Unsupported report type');
    }
  }

  private async generatePDFReport(data: any, reportDto: GenerateReportDto): Promise<Buffer> {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      
      // Load HTML template
      const templatePath = path.join(__dirname, '../templates', `${reportDto.type}.hbs`);
      let template: string;
      
      try {
        template = await fs.readFile(templatePath, 'utf-8');
      } catch (error) {
        // Use default template if specific template not found
        template = await this.getDefaultTemplate(reportDto.type);
      }

      const compiledTemplate = Handlebars.compile(template);
      const html = compiledTemplate({
        ...data,
        title: reportDto.title || this.getDefaultTitle(reportDto.type),
        generatedAt: moment().format('YYYY-MM-DD HH:mm:ss'),
        generatedAtArabic: moment().format('DD/MM/YYYY - HH:mm'),
        includeCharts: reportDto.includeCharts
      });

      await page.setContent(html, { waitUntil: 'networkidle0' });
      
      // Configure PDF options for Arabic support
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm'
        },
        displayHeaderFooter: true,
        headerTemplate: `
          <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
            ${reportDto.title || this.getDefaultTitle(reportDto.type)}
          </div>
        `,
        footerTemplate: `
          <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
            <span>صفحة <span class="pageNumber"></span> من <span class="totalPages"></span></span>
            <span style="float: right;">تم الإنشاء في: ${moment().format('DD/MM/YYYY')}</span>
          </div>
        `
      });

      return pdfBuffer;
    } finally {
      await browser.close();
    }
  }

  private async generateExcelReport(data: any, reportDto: GenerateReportDto): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(reportDto.title || this.getDefaultTitle(reportDto.type));

    // Configure worksheet for Arabic
    worksheet.views = [{ rightToLeft: true }];

    // Add title
    const titleRow = worksheet.addRow([reportDto.title || this.getDefaultTitle(reportDto.type)]);
    titleRow.font = { size: 16, bold: true };
    titleRow.alignment = { horizontal: 'center' };
    worksheet.mergeCells('A1:Z1');

    // Add generation date
    const dateRow = worksheet.addRow([`تاريخ الإنشاء: ${moment().format('DD/MM/YYYY HH:mm')}`]);
    dateRow.font = { size: 12 };
    dateRow.alignment = { horizontal: 'center' };
    worksheet.mergeCells('A2:Z2');

    // Add empty row
    worksheet.addRow([]);

    // Generate content based on report type
    switch (reportDto.type) {
      case ReportType.EMPLOYEE_LIST:
        this.addEmployeeListToExcel(worksheet, data);
        break;
      case ReportType.ATTENDANCE_SUMMARY:
        this.addAttendanceSummaryToExcel(worksheet, data);
        break;
      case ReportType.LEAVE_REPORT:
        this.addLeaveReportToExcel(worksheet, data);
        break;
      default:
        this.addGenericDataToExcel(worksheet, data);
    }

    // Auto-fit columns
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    return workbook.xlsx.writeBuffer() as Promise<Buffer>;
  }

  private async generateWordReport(data: any, reportDto: GenerateReportDto): Promise<Buffer> {
    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Title
          new Paragraph({
            children: [
              new TextRun({
                text: reportDto.title || this.getDefaultTitle(reportDto.type),
                bold: true,
                size: 32,
              }),
            ],
            alignment: 'center',
            spacing: { after: 400 },
          }),
          
          // Generation date
          new Paragraph({
            children: [
              new TextRun({
                text: `تاريخ الإنشاء: ${moment().format('DD/MM/YYYY HH:mm')}`,
                size: 24,
              }),
            ],
            alignment: 'center',
            spacing: { after: 400 },
          }),

          // Content based on report type
          ...this.generateWordContent(data, reportDto.type)
        ],
      }],
    });

    return Packer.toBuffer(doc);
  }

  private generateWordContent(data: any, type: ReportType): Paragraph[] {
    const content: Paragraph[] = [];

    switch (type) {
      case ReportType.EMPLOYEE_LIST:
        if (data.employees && data.employees.length > 0) {
          content.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `إجمالي الموظفين: ${data.employees.length}`,
                  bold: true,
                  size: 24,
                }),
              ],
              spacing: { after: 200 },
            })
          );

          // Add employee table (simplified)
          data.employees.forEach((employee: any, index: number) => {
            content.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${index + 1}. ${employee.personalInfo?.fullName || 'غير محدد'} - ${employee.personalInfo?.militaryNumber || 'غير محدد'}`,
                    size: 20,
                  }),
                ],
                spacing: { after: 100 },
              })
            );
          });
        }
        break;

      default:
        content.push(
          new Paragraph({
            children: [
              new TextRun({
                text: 'بيانات التقرير',
                size: 24,
              }),
            ],
          })
        );
    }

    return content;
  }

  private async fetchEmployeeListData(filters?: any): Promise<any> {
    // Mock data - in real implementation, this would call the employee service
    return {
      employees: [
        {
          personalInfo: {
            fullName: 'أحمد محمد علي',
            militaryNumber: 'M001',
            nationalId: '1234567890',
            phoneNumber: '0501234567'
          },
          jobInfo: {
            rank: 'رقيب أول',
            unit: 'الوحدة الأولى',
            position: 'مسؤول إداري',
            salary: 8000
          }
        }
      ],
      summary: {
        totalEmployees: 1,
        activeEmployees: 1,
        inactiveEmployees: 0
      }
    };
  }

  private async fetchAttendanceSummaryData(filters?: any): Promise<any> {
    return {
      summary: {
        totalDays: 30,
        presentDays: 28,
        absentDays: 2,
        lateArrivals: 3,
        earlyDepartures: 1
      },
      details: []
    };
  }

  private async fetchLeaveReportData(filters?: any): Promise<any> {
    return {
      leaves: [],
      summary: {
        totalRequests: 0,
        approvedRequests: 0,
        pendingRequests: 0,
        rejectedRequests: 0
      }
    };
  }

  private async fetchPayrollReportData(filters?: any): Promise<any> {
    return {
      payroll: [],
      summary: {
        totalSalaries: 0,
        totalDeductions: 0,
        netPay: 0
      }
    };
  }

  private async fetchPerformanceReportData(filters?: any): Promise<any> {
    return {
      performance: [],
      summary: {
        averageRating: 0,
        topPerformers: [],
        improvementNeeded: []
      }
    };
  }

  private async fetchDisciplinaryReportData(filters?: any): Promise<any> {
    return {
      disciplinary: [],
      summary: {
        totalCases: 0,
        resolvedCases: 0,
        pendingCases: 0
      }
    };
  }

  private addEmployeeListToExcel(worksheet: ExcelJS.Worksheet, data: any): void {
    // Add headers
    const headerRow = worksheet.addRow([
      'الرقم العسكري',
      'الاسم الكامل',
      'الرتبة',
      'الوحدة',
      'المنصب',
      'الراتب',
      'رقم الهاتف'
    ]);

    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add data rows
    if (data.employees) {
      data.employees.forEach((employee: any) => {
        worksheet.addRow([
          employee.personalInfo?.militaryNumber || '',
          employee.personalInfo?.fullName || '',
          employee.jobInfo?.rank || '',
          employee.jobInfo?.unit || '',
          employee.jobInfo?.position || '',
          employee.jobInfo?.salary || 0,
          employee.personalInfo?.phoneNumber || ''
        ]);
      });
    }
  }

  private addAttendanceSummaryToExcel(worksheet: ExcelJS.Worksheet, data: any): void {
    // Add summary section
    worksheet.addRow(['ملخص الحضور']);
    worksheet.addRow(['إجمالي الأيام', data.summary?.totalDays || 0]);
    worksheet.addRow(['أيام الحضور', data.summary?.presentDays || 0]);
    worksheet.addRow(['أيام الغياب', data.summary?.absentDays || 0]);
    worksheet.addRow(['التأخير', data.summary?.lateArrivals || 0]);
    worksheet.addRow(['المغادرة المبكرة', data.summary?.earlyDepartures || 0]);
  }

  private addLeaveReportToExcel(worksheet: ExcelJS.Worksheet, data: any): void {
    // Add headers
    const headerRow = worksheet.addRow([
      'اسم الموظف',
      'نوع الإجازة',
      'تاريخ البداية',
      'تاريخ النهاية',
      'عدد الأيام',
      'الحالة',
      'السبب'
    ]);

    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add data rows
    if (data.leaves) {
      data.leaves.forEach((leave: any) => {
        worksheet.addRow([
          leave.employeeId?.personalInfo?.fullName || '',
          leave.type || '',
          moment(leave.startDate).format('DD/MM/YYYY'),
          moment(leave.endDate).format('DD/MM/YYYY'),
          leave.totalDays || 0,
          leave.status || '',
          leave.reason || ''
        ]);
      });
    }
  }

  private addGenericDataToExcel(worksheet: ExcelJS.Worksheet, data: any): void {
    // Add generic data representation
    worksheet.addRow(['البيانات']);
    worksheet.addRow([JSON.stringify(data, null, 2)]);
  }

  private getDefaultTitle(type: ReportType): string {
    const titles = {
      [ReportType.EMPLOYEE_LIST]: 'قائمة الموظفين',
      [ReportType.ATTENDANCE_SUMMARY]: 'ملخص الحضور والغياب',
      [ReportType.LEAVE_REPORT]: 'تقرير الإجازات',
      [ReportType.PAYROLL_REPORT]: 'تقرير الرواتب',
      [ReportType.PERFORMANCE_REPORT]: 'تقرير الأداء',
      [ReportType.DISCIPLINARY_REPORT]: 'تقرير التأديب'
    };

    return titles[type] || 'تقرير';
  }

  private async getDefaultTemplate(type: ReportType): Promise<string> {
    // Return a basic HTML template
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{{title}}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
          }
          .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .date {
            font-size: 14px;
            color: #666;
          }
          .content {
            margin-top: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
          }
          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">{{title}}</div>
          <div class="date">تاريخ الإنشاء: {{generatedAtArabic}}</div>
        </div>
        <div class="content">
          <p>محتوى التقرير سيتم عرضه هنا</p>
        </div>
      </body>
      </html>
    `;
  }

  private registerHandlebarsHelpers(): void {
    // Register Arabic date helper
    Handlebars.registerHelper('arabicDate', function(date: Date) {
      return moment(date).format('DD/MM/YYYY');
    });

    // Register Arabic number helper
    Handlebars.registerHelper('arabicNumber', function(number: number) {
      return number.toLocaleString('ar-SA');
    });

    // Register conditional helper
    Handlebars.registerHelper('ifEquals', function(arg1: any, arg2: any, options: any) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });
  }
}

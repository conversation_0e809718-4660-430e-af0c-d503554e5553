import { <PERSON>p, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { 
  BloodType, 
  MaritalStatus, 
  JobStatus,
  PunishmentType,
  PunishmentStatus,
  RewardType
} from '@shared/types/employee.types';

export type EmployeeDocument = Employee & Document;

@Schema({ _id: false })
export class PersonalInfo {
  @Prop({ required: true, trim: true })
  fullName: string;

  @Prop({ trim: true })
  fullNameEn: string;

  @Prop({ required: true, unique: true })
  nationalId: string;

  @Prop({ required: true, unique: true })
  militaryNumber: string;

  @Prop({ required: true })
  motherName: string;

  @Prop({ required: true, enum: Object.values(BloodType) })
  bloodType: BloodType;

  @Prop()
  photo: string;

  @Prop({ required: true })
  dateOfBirth: Date;

  @Prop({ required: true })
  placeOfBirth: string;

  @Prop({ required: true, default: 'سعودي' })
  nationality: string;

  @Prop({ required: true, enum: Object.values(MaritalStatus) })
  maritalStatus: MaritalStatus;

  @Prop({ required: true })
  phoneNumber: string;

  @Prop({
    type: {
      name: { type: String, required: true },
      relationship: { type: String, required: true },
      phone: { type: String, required: true }
    },
    required: true
  })
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };

  @Prop({
    type: {
      street: { type: String, required: true },
      city: { type: String, required: true },
      province: { type: String, required: true },
      postalCode: { type: String }
    },
    required: true
  })
  address: {
    street: string;
    city: string;
    province: string;
    postalCode?: string;
  };
}

@Schema({ _id: false })
export class JobInfo {
  @Prop({ required: true })
  rank: string;

  @Prop({ required: true })
  unit: string;

  @Prop({ required: true })
  position: string;

  @Prop({ required: true })
  location: string;

  @Prop({ required: true })
  appointmentDate: Date;

  @Prop()
  lastPromotionDate: Date;

  @Prop({ required: true, enum: Object.values(JobStatus), default: JobStatus.ACTIVE })
  jobStatus: JobStatus;

  @Prop({ required: true, min: 0 })
  salary: number;

  @Prop({
    type: {
      bankName: { type: String, required: true },
      accountNumber: { type: String, required: true },
      iban: { type: String }
    },
    required: true
  })
  bankInfo: {
    bankName: string;
    accountNumber: string;
    iban?: string;
  };
}

@Schema({ _id: false })
export class Punishment {
  @Prop({ required: true, enum: Object.values(PunishmentType) })
  type: PunishmentType;

  @Prop({ required: true })
  reason: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  date: Date;

  @Prop({ min: 0 })
  duration: number;

  @Prop({ required: true })
  issuedBy: string;

  @Prop({ required: true, enum: Object.values(PunishmentStatus), default: PunishmentStatus.ACTIVE })
  status: PunishmentStatus;

  @Prop([String])
  documents: string[];
}

@Schema({ _id: false })
export class Reward {
  @Prop({ required: true, enum: Object.values(RewardType) })
  type: RewardType;

  @Prop({ required: true })
  reason: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  date: Date;

  @Prop({ min: 0 })
  amount: number;

  @Prop({ required: true })
  issuedBy: string;

  @Prop([String])
  documents: string[];
}

@Schema({ _id: false })
export class AdministrativeInfo {
  @Prop({ required: true, default: 30, min: 0 })
  leaveBalance: number;

  @Prop({ required: true, default: 0, min: 0 })
  totalLeavesTaken: number;

  @Prop({ type: [Punishment], default: [] })
  punishments: Punishment[];

  @Prop({ type: [Reward], default: [] })
  rewards: Reward[];

  @Prop({
    type: {
      medicalConditions: [String],
      allergies: [String],
      medications: [String]
    }
  })
  medicalInfo: {
    medicalConditions?: string[];
    allergies?: string[];
    medications?: string[];
  };
}

@Schema({ _id: false })
export class SystemInfo {
  @Prop({ required: true, unique: true })
  barcode: string;

  @Prop()
  qrCode: string;

  @Prop({ required: true, default: true })
  isActive: boolean;

  @Prop({ required: true })
  createdBy: string;

  @Prop({ required: true })
  lastModifiedBy: string;
}

@Schema({
  timestamps: true,
  collection: 'employees'
})
export class Employee {
  @Prop({ type: PersonalInfo, required: true })
  personalInfo: PersonalInfo;

  @Prop({ type: JobInfo, required: true })
  jobInfo: JobInfo;

  @Prop({ type: AdministrativeInfo, required: true })
  administrativeInfo: AdministrativeInfo;

  @Prop({ type: SystemInfo, required: true })
  systemInfo: SystemInfo;
}

export const EmployeeSchema = SchemaFactory.createForClass(Employee);

// Indexes for better performance
EmployeeSchema.index({ 'personalInfo.militaryNumber': 1 });
EmployeeSchema.index({ 'personalInfo.nationalId': 1 });
EmployeeSchema.index({ 'jobInfo.unit': 1 });
EmployeeSchema.index({ 'jobInfo.rank': 1 });
EmployeeSchema.index({ 'jobInfo.jobStatus': 1 });
EmployeeSchema.index({ 'systemInfo.barcode': 1 });
EmployeeSchema.index({ 'systemInfo.isActive': 1 });
EmployeeSchema.index({ 'personalInfo.fullName': 'text' });

// Pre-save middleware to generate barcode if not provided
EmployeeSchema.pre('save', function(next) {
  if (!this.systemInfo.barcode) {
    const prefix = process.env.BARCODE_PREFIX || 'EMP';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    this.systemInfo.barcode = `${prefix}${timestamp}${random}`;
  }
  next();
});

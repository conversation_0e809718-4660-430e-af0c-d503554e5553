/* Login Page Styles */
.login-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family);
}

.login-container {
    position: relative;
    width: 100%;
    max-width: 450px;
    margin: var(--spacing-4);
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    border-radius: var(--radius-2xl);
    opacity: 0.3;
}

.pattern-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.02) 50%, transparent 70%);
    border-radius: var(--radius-2xl);
}

.login-card {
    position: relative;
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
    box-shadow: var(--shadow-lg);
}

.logo i {
    font-size: var(--font-size-3xl);
    color: var(--white);
}

.login-header h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-2);
}

.login-header p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-bottom: 0;
}

.login-form {
    margin-bottom: var(--spacing-6);
}

.login-form .form-group {
    margin-bottom: var(--spacing-6);
}

.login-form label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-3);
}

.login-form label i {
    color: var(--primary-color);
    width: 16px;
}

.login-form input {
    width: 100%;
    padding: var(--spacing-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    background: var(--gray-50);
}

.login-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--white);
    box-shadow: 0 0 0 4px rgb(37 99 235 / 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: var(--spacing-4);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: color var(--transition-fast);
}

.toggle-password:hover {
    color: var(--primary-color);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.checkbox-container input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    position: relative;
}

.forgot-password {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: var(--spacing-4);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    box-shadow: var(--shadow-md);
}

.login-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.error-message,
.success-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.error-message {
    background: rgb(239 68 68 / 0.1);
    color: var(--danger-color);
    border: 1px solid rgb(239 68 68 / 0.2);
}

.success-message {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
    border: 1px solid rgb(16 185 129 / 0.2);
}

.demo-credentials {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    border: 1px solid var(--gray-200);
}

.demo-credentials h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-3);
}

.demo-credentials h4 i {
    color: var(--info-color);
}

.credentials-grid {
    display: grid;
    gap: var(--spacing-2);
}

.credential-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    padding: var(--spacing-2);
    background: var(--white);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.credential-item strong {
    color: var(--gray-700);
}

.credential-item span {
    font-family: monospace;
    background: var(--gray-100);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    color: var(--gray-600);
}

.login-footer {
    text-align: center;
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

.login-footer p {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-bottom: var(--spacing-3);
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
}

.footer-links a {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.system-status {
    position: fixed;
    bottom: var(--spacing-4);
    right: var(--spacing-4);
    background: var(--white);
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xs);
}

.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        margin: var(--spacing-2);
    }
    
    .login-card {
        padding: var(--spacing-6);
    }
    
    .logo {
        width: 60px;
        height: 60px;
    }
    
    .logo i {
        font-size: var(--font-size-2xl);
    }
    
    .login-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;
    }
    
    .credentials-grid {
        gap: var(--spacing-1);
    }
    
    .credential-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-1);
    }
    
    .footer-links {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .system-status {
        position: static;
        margin-top: var(--spacing-4);
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading states */
.login-btn i.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

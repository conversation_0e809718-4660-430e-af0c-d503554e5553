import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as sharp from 'sharp';
import * as path from 'path';
import * as fs from 'fs/promises';

import { Employee, EmployeeDocument } from './schemas/employee.schema';
import { 
  CreateEmployeeDto, 
  UpdateEmployeeDto,
  JobStatus 
} from '@shared/types/employee.types';

@Injectable()
export class EmployeesService {
  constructor(
    @InjectModel(Employee.name) private employeeModel: Model<EmployeeDocument>,
  ) {}

  async create(createEmployeeDto: CreateEmployeeDto, createdBy: string): Promise<Employee> {
    // Check for duplicate military number or national ID
    const existingEmployee = await this.employeeModel.findOne({
      $or: [
        { 'personalInfo.militaryNumber': createEmployeeDto.personalInfo.militaryNumber },
        { 'personalInfo.nationalId': createEmployeeDto.personalInfo.nationalId }
      ]
    });

    if (existingEmployee) {
      if (existingEmployee.personalInfo.militaryNumber === createEmployeeDto.personalInfo.militaryNumber) {
        throw new ConflictException('Military number already exists');
      }
      if (existingEmployee.personalInfo.nationalId === createEmployeeDto.personalInfo.nationalId) {
        throw new ConflictException('National ID already exists');
      }
    }

    // Generate barcode
    const barcode = this.generateBarcode();

    const employee = new this.employeeModel({
      personalInfo: createEmployeeDto.personalInfo,
      jobInfo: createEmployeeDto.jobInfo,
      administrativeInfo: {
        leaveBalance: 30,
        totalLeavesTaken: 0,
        punishments: [],
        rewards: [],
        ...createEmployeeDto.administrativeInfo
      },
      systemInfo: {
        barcode,
        isActive: true,
        createdBy,
        lastModifiedBy: createdBy
      }
    });

    return employee.save();
  }

  async findAll(
    page: number = 1, 
    limit: number = 10, 
    filters?: any
  ): Promise<{ employees: Employee[], total: number }> {
    const skip = (page - 1) * limit;
    const query = this.buildQuery(filters);

    const [employees, total] = await Promise.all([
      this.employeeModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.employeeModel.countDocuments(query)
    ]);

    return { employees, total };
  }

  async findOne(id: string): Promise<Employee> {
    const employee = await this.employeeModel.findById(id).exec();
    if (!employee) {
      throw new NotFoundException('Employee not found');
    }
    return employee;
  }

  async findByMilitaryNumber(militaryNumber: string): Promise<Employee> {
    const employee = await this.employeeModel
      .findOne({ 'personalInfo.militaryNumber': militaryNumber })
      .exec();
    
    if (!employee) {
      throw new NotFoundException('Employee not found');
    }
    
    return employee;
  }

  async findByBarcode(barcode: string): Promise<Employee> {
    const employee = await this.employeeModel
      .findOne({ 'systemInfo.barcode': barcode })
      .exec();
    
    if (!employee) {
      throw new NotFoundException('Employee not found');
    }
    
    return employee;
  }

  async update(id: string, updateEmployeeDto: UpdateEmployeeDto, updatedBy: string): Promise<Employee> {
    // Check for conflicts if updating military number or national ID
    if (updateEmployeeDto.personalInfo?.militaryNumber || updateEmployeeDto.personalInfo?.nationalId) {
      const conflictQuery: any = { _id: { $ne: id } };
      const orConditions: any[] = [];

      if (updateEmployeeDto.personalInfo.militaryNumber) {
        orConditions.push({ 'personalInfo.militaryNumber': updateEmployeeDto.personalInfo.militaryNumber });
      }
      if (updateEmployeeDto.personalInfo.nationalId) {
        orConditions.push({ 'personalInfo.nationalId': updateEmployeeDto.personalInfo.nationalId });
      }

      if (orConditions.length > 0) {
        conflictQuery.$or = orConditions;
        const existingEmployee = await this.employeeModel.findOne(conflictQuery);
        
        if (existingEmployee) {
          if (existingEmployee.personalInfo.militaryNumber === updateEmployeeDto.personalInfo?.militaryNumber) {
            throw new ConflictException('Military number already exists');
          }
          if (existingEmployee.personalInfo.nationalId === updateEmployeeDto.personalInfo?.nationalId) {
            throw new ConflictException('National ID already exists');
          }
        }
      }
    }

    const updateData: any = {};
    
    if (updateEmployeeDto.personalInfo) {
      Object.keys(updateEmployeeDto.personalInfo).forEach(key => {
        updateData[`personalInfo.${key}`] = updateEmployeeDto.personalInfo[key];
      });
    }
    
    if (updateEmployeeDto.jobInfo) {
      Object.keys(updateEmployeeDto.jobInfo).forEach(key => {
        updateData[`jobInfo.${key}`] = updateEmployeeDto.jobInfo[key];
      });
    }
    
    if (updateEmployeeDto.administrativeInfo) {
      Object.keys(updateEmployeeDto.administrativeInfo).forEach(key => {
        updateData[`administrativeInfo.${key}`] = updateEmployeeDto.administrativeInfo[key];
      });
    }

    updateData['systemInfo.lastModifiedBy'] = updatedBy;

    const employee = await this.employeeModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .exec();

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    return employee;
  }

  async remove(id: string): Promise<void> {
    const result = await this.employeeModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('Employee not found');
    }
  }

  async toggleStatus(id: string, updatedBy: string): Promise<Employee> {
    const employee = await this.findOne(id);

    const updatedEmployee = await this.employeeModel
      .findByIdAndUpdate(
        id,
        {
          'systemInfo.isActive': !employee.systemInfo.isActive,
          'systemInfo.lastModifiedBy': updatedBy
        },
        { new: true }
      )
      .exec();

    return updatedEmployee;
  }

  async uploadPhoto(id: string, file: any): Promise<Employee> {
    await this.findOne(id); // Validate employee exists

    // Process and save image
    const filename = `employee_${id}_${Date.now()}.jpg`;
    const uploadPath = path.join(process.env.UPLOAD_PATH || './uploads', 'photos');

    // Ensure upload directory exists
    await fs.mkdir(uploadPath, { recursive: true });

    const filePath = path.join(uploadPath, filename);

    // Resize and optimize image
    await sharp(file.buffer)
      .resize(300, 400, { fit: 'cover' })
      .jpeg({ quality: 85 })
      .toFile(filePath);

    // Update employee photo path
    const updatedEmployee = await this.employeeModel
      .findByIdAndUpdate(
        id,
        { 'personalInfo.photo': `/uploads/photos/${filename}` },
        { new: true }
      )
      .exec();

    return updatedEmployee;
  }

  async addPunishment(id: string, punishment: any, issuedBy: string): Promise<Employee> {
    const employee = await this.findOne(id);

    const newPunishment = {
      ...punishment,
      issuedBy,
      date: new Date()
    };

    const updatedEmployee = await this.employeeModel
      .findByIdAndUpdate(
        id,
        {
          $push: { 'administrativeInfo.punishments': newPunishment },
          'systemInfo.lastModifiedBy': issuedBy
        },
        { new: true }
      )
      .exec();

    return updatedEmployee;
  }

  async addReward(id: string, reward: any, issuedBy: string): Promise<Employee> {
    const employee = await this.findOne(id);

    const newReward = {
      ...reward,
      issuedBy,
      date: new Date()
    };

    const updatedEmployee = await this.employeeModel
      .findByIdAndUpdate(
        id,
        {
          $push: { 'administrativeInfo.rewards': newReward },
          'systemInfo.lastModifiedBy': issuedBy
        },
        { new: true }
      )
      .exec();

    return updatedEmployee;
  }

  async updateLeaveBalance(id: string, balance: number, updatedBy: string): Promise<Employee> {
    if (balance < 0) {
      throw new BadRequestException('Leave balance cannot be negative');
    }

    const updatedEmployee = await this.employeeModel
      .findByIdAndUpdate(
        id,
        {
          'administrativeInfo.leaveBalance': balance,
          'systemInfo.lastModifiedBy': updatedBy
        },
        { new: true }
      )
      .exec();

    if (!updatedEmployee) {
      throw new NotFoundException('Employee not found');
    }

    return updatedEmployee;
  }

  async getStatistics(): Promise<any> {
    const [
      totalEmployees,
      activeEmployees,
      inactiveEmployees,
      onLeaveEmployees,
      retiredEmployees,
      unitStats,
      rankStats
    ] = await Promise.all([
      this.employeeModel.countDocuments(),
      this.employeeModel.countDocuments({ 'systemInfo.isActive': true }),
      this.employeeModel.countDocuments({ 'systemInfo.isActive': false }),
      this.employeeModel.countDocuments({ 'jobInfo.jobStatus': JobStatus.ON_LEAVE }),
      this.employeeModel.countDocuments({ 'jobInfo.jobStatus': JobStatus.RETIRED }),
      this.employeeModel.aggregate([
        { $group: { _id: '$jobInfo.unit', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      this.employeeModel.aggregate([
        { $group: { _id: '$jobInfo.rank', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ])
    ]);

    return {
      totalEmployees,
      activeEmployees,
      inactiveEmployees,
      onLeaveEmployees,
      retiredEmployees,
      unitDistribution: unitStats,
      rankDistribution: rankStats
    };
  }

  private buildQuery(filters?: any): any {
    const query: any = {};

    if (filters) {
      if (filters.unit) {
        query['jobInfo.unit'] = filters.unit;
      }
      if (filters.rank) {
        query['jobInfo.rank'] = filters.rank;
      }
      if (filters.status) {
        query['jobInfo.jobStatus'] = filters.status;
      }
      if (filters.isActive !== undefined) {
        query['systemInfo.isActive'] = filters.isActive;
      }
      if (filters.search) {
        query.$or = [
          { 'personalInfo.fullName': { $regex: filters.search, $options: 'i' } },
          { 'personalInfo.militaryNumber': { $regex: filters.search, $options: 'i' } },
          { 'personalInfo.nationalId': { $regex: filters.search, $options: 'i' } }
        ];
      }
      if (filters.appointmentDateFrom || filters.appointmentDateTo) {
        query['jobInfo.appointmentDate'] = {};
        if (filters.appointmentDateFrom) {
          query['jobInfo.appointmentDate'].$gte = new Date(filters.appointmentDateFrom);
        }
        if (filters.appointmentDateTo) {
          query['jobInfo.appointmentDate'].$lte = new Date(filters.appointmentDateTo);
        }
      }
    }

    return query;
  }

  private generateBarcode(): string {
    const prefix = process.env.BARCODE_PREFIX || 'EMP';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }
}

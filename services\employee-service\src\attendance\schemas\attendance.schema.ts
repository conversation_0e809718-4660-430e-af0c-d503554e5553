import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { AttendanceStatus, AttendanceMethod } from '@shared/types/employee.types';

export type AttendanceDocument = Attendance & Document;

@Schema({
  timestamps: true,
  collection: 'attendance'
})
export class Attendance {
  @Prop({ type: Types.ObjectId, ref: 'Employee', required: true })
  employeeId: Types.ObjectId;

  @Prop({ required: true })
  date: Date;

  @Prop()
  checkIn: Date;

  @Prop()
  checkOut: Date;

  @Prop({ 
    required: true, 
    enum: Object.values(AttendanceStatus),
    default: AttendanceStatus.PRESENT 
  })
  status: AttendanceStatus;

  @Prop()
  notes: string;

  @Prop()
  location: string;

  @Prop({ 
    required: true, 
    enum: Object.values(AttendanceMethod),
    default: AttendanceMethod.MANUAL 
  })
  method: AttendanceMethod;

  @Prop()
  workingHours: number; // Calculated working hours

  @Prop()
  overtimeHours: number; // Calculated overtime hours

  @Prop({ default: false })
  isLate: boolean;

  @Prop({ default: false })
  isEarlyLeave: boolean;

  @Prop()
  recordedBy: string; // User who recorded the attendance
}

export const AttendanceSchema = SchemaFactory.createForClass(Attendance);

// Indexes
AttendanceSchema.index({ employeeId: 1 });
AttendanceSchema.index({ date: 1 });
AttendanceSchema.index({ employeeId: 1, date: 1 }, { unique: true });
AttendanceSchema.index({ status: 1 });
AttendanceSchema.index({ method: 1 });

// Pre-save middleware to calculate working hours and overtime
AttendanceSchema.pre('save', function(next) {
  if (this.checkIn && this.checkOut) {
    const workStart = new Date(this.checkIn);
    const workEnd = new Date(this.checkOut);
    const diffMs = workEnd.getTime() - workStart.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    
    this.workingHours = Math.max(0, diffHours);
    
    // Standard working hours (8 hours)
    const standardHours = 8;
    this.overtimeHours = Math.max(0, diffHours - standardHours);
    
    // Check if late (assuming work starts at 8:00 AM)
    const workStartTime = new Date(this.date);
    workStartTime.setHours(8, 0, 0, 0);
    this.isLate = this.checkIn > workStartTime;
    
    // Check if early leave (assuming work ends at 4:00 PM)
    const workEndTime = new Date(this.date);
    workEndTime.setHours(16, 0, 0, 0);
    this.isEarlyLeave = this.checkOut < workEndTime;
  }
  
  next();
});

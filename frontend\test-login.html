<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تسجيل الدخول</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            
            <button type="submit">اختبار تسجيل الدخول</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = 'جاري الاختبار...';
            resultDiv.className = 'result';
            
            try {
                console.log('Sending login request...');
                console.log('Username:', username);
                console.log('Password:', password);
                
                const response = await fetch('http://localhost:3000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `✅ نجح تسجيل الدخول!
                    
الاستجابة:
${JSON.stringify(data, null, 2)}

سيتم التوجيه إلى لوحة التحكم خلال 3 ثوان...`;
                    resultDiv.className = 'result success';
                    
                    // Store auth data
                    localStorage.setItem('authToken', data.data.token);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    localStorage.setItem('userData', JSON.stringify(data.data.user));
                    
                    // Redirect after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 3000);
                    
                } else {
                    resultDiv.innerHTML = `❌ فشل تسجيل الدخول!
                    
الخطأ: ${data.message}

الاستجابة الكاملة:
${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.innerHTML = `❌ خطأ في الاتصال!
                
تفاصيل الخطأ:
${error.message}

Stack trace:
${error.stack}`;
                resultDiv.className = 'result error';
            }
        });
        
        // Test on page load
        window.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, testing API availability...');
            
            fetch('http://localhost:3000/health')
                .then(response => response.json())
                .then(data => {
                    console.log('Health check successful:', data);
                })
                .catch(error => {
                    console.error('Health check failed:', error);
                });
        });
    </script>
</body>
</html>

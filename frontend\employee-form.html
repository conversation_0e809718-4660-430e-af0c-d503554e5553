<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة/تعديل موظف - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .form-header {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-sm);
        }
        
        .form-sections {
            display: grid;
            gap: 20px;
        }
        
        .form-section {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
        }
        
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--border-color);
            color: var(--primary-color);
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .photo-upload {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            padding: 20px;
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            text-align: center;
        }
        
        .photo-preview {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--primary-color);
        }
        
        .photo-placeholder {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--gray-400);
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            padding: 10px 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .upload-btn:hover {
            background: var(--primary-dark);
        }
        
        .form-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
            margin-top: 30px;
            padding: 20px;
            background: var(--card-bg);
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            color: var(--text-muted);
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .required {
            color: var(--danger-color);
        }
        
        .form-help {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="employees.html" class="nav-link active">
                    <i class="fas fa-users"></i>
                    الموظفين
                </a>
                <a href="attendance.html" class="nav-link">
                    <i class="fas fa-clock"></i>
                    الحضور
                </a>
                <a href="leaves.html" class="nav-link">
                    <i class="fas fa-calendar-alt"></i>
                    الإجازات
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="userName">المدير</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="form-container">
            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="index.html">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
                <i class="fas fa-chevron-left"></i>
                <a href="employees.html">الموظفين</a>
                <i class="fas fa-chevron-left"></i>
                <span id="breadcrumbTitle">إضافة موظف جديد</span>
            </div>

            <!-- Form Header -->
            <div class="form-header">
                <h1 id="formTitle">
                    <i class="fas fa-user-plus"></i>
                    إضافة موظف جديد
                </h1>
                <p id="formDescription">يرجى ملء جميع البيانات المطلوبة بدقة</p>
            </div>

            <!-- Employee Form -->
            <form id="employeeForm" novalidate>
                <input type="hidden" id="employeeId" name="employeeId">
                
                <!-- Personal Information Section -->
                <div class="form-section">
                    <h2 class="section-title">
                        <i class="fas fa-user"></i>
                        البيانات الشخصية
                    </h2>
                    
                    <div class="form-grid">
                        <!-- Photo Upload -->
                        <div class="form-group">
                            <label>صورة الموظف:</label>
                            <div class="photo-upload">
                                <div id="photoPreview" class="photo-placeholder">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <input type="file" id="photoInput" class="file-input" accept="image/*" onchange="handlePhotoUpload(event)">
                                <button type="button" class="upload-btn" onclick="document.getElementById('photoInput').click()">
                                    <i class="fas fa-upload"></i>
                                    اختيار صورة
                                </button>
                                <div class="form-help">الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG</div>
                            </div>
                        </div>
                        
                        <!-- Basic Info -->
                        <div class="form-group">
                            <label for="fullName">الاسم الكامل <span class="required">*</span>:</label>
                            <input type="text" id="fullName" name="fullName" required>
                            <div class="form-help">الاسم الثلاثي أو الرباعي كاملاً</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="militaryNumber">الرقم العسكري <span class="required">*</span>:</label>
                            <input type="text" id="militaryNumber" name="militaryNumber" required>
                            <div class="form-help">رقم فريد لا يمكن تكراره</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="nationalId">رقم الهوية الوطنية <span class="required">*</span>:</label>
                            <input type="text" id="nationalId" name="nationalId" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="birthDate">تاريخ الميلاد <span class="required">*</span>:</label>
                            <input type="date" id="birthDate" name="birthDate" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="gender">الجنس <span class="required">*</span>:</label>
                            <select id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="MALE">ذكر</option>
                                <option value="FEMALE">أنثى</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="bloodType">فصيلة الدم:</label>
                            <select id="bloodType" name="bloodType">
                                <option value="">اختر فصيلة الدم</option>
                                <option value="A+">A+</option>
                                <option value="A-">A-</option>
                                <option value="B+">B+</option>
                                <option value="B-">B-</option>
                                <option value="AB+">AB+</option>
                                <option value="AB-">AB-</option>
                                <option value="O+">O+</option>
                                <option value="O-">O-</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="motherName">اسم الأم <span class="required">*</span>:</label>
                            <input type="text" id="motherName" name="motherName" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="maritalStatus">الحالة الاجتماعية:</label>
                            <select id="maritalStatus" name="maritalStatus">
                                <option value="">اختر الحالة</option>
                                <option value="SINGLE">أعزب</option>
                                <option value="MARRIED">متزوج</option>
                                <option value="DIVORCED">مطلق</option>
                                <option value="WIDOWED">أرمل</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="form-section">
                    <h2 class="section-title">
                        <i class="fas fa-address-book"></i>
                        معلومات الاتصال
                    </h2>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="phone">رقم الهاتف <span class="required">*</span>:</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني:</label>
                            <input type="email" id="email" name="email">
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="address">العنوان:</label>
                            <textarea id="address" name="address" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Military Information Section -->
                <div class="form-section">
                    <h2 class="section-title">
                        <i class="fas fa-medal"></i>
                        البيانات العسكرية
                    </h2>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="rank">الرتبة <span class="required">*</span>:</label>
                            <select id="rank" name="rank" required>
                                <option value="">اختر الرتبة</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="unit">الوحدة <span class="required">*</span>:</label>
                            <select id="unit" name="unit" required>
                                <option value="">اختر الوحدة</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="position">المنصب <span class="required">*</span>:</label>
                            <input type="text" id="position" name="position" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="appointmentDate">تاريخ التعيين <span class="required">*</span>:</label>
                            <input type="date" id="appointmentDate" name="appointmentDate" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="serviceYears">سنوات الخدمة:</label>
                            <input type="number" id="serviceYears" name="serviceYears" min="0" readonly>
                            <div class="form-help">يتم حسابها تلقائياً من تاريخ التعيين</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="status">حالة الموظف <span class="required">*</span>:</label>
                            <select id="status" name="status" required>
                                <option value="">اختر الحالة</option>
                                <option value="ACTIVE">نشط</option>
                                <option value="INACTIVE">غير نشط</option>
                                <option value="ON_LEAVE">في إجازة</option>
                                <option value="SUSPENDED">موقوف</option>
                                <option value="RETIRED">متقاعد</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Form Actions -->
            <div class="form-actions">
                <div class="form-info">
                    <span class="required">*</span> الحقول المطلوبة
                </div>
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-info" onclick="resetForm()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                    <button type="submit" form="employeeForm" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        <span id="saveButtonText">حفظ الموظف</span>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري الحفظ...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/employees.js"></script>
    <script src="js/main.js"></script>

    <script>
        // Form state
        let isEditMode = false;
        let currentEmployeeId = null;

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!isAuthenticated()) {
                redirectToLogin();
                return;
            }

            // Initialize form
            initializeEmployeeForm();
        });

        // Initialize employee form
        async function initializeEmployeeForm() {
            try {
                // Load form options
                loadFormOptions();

                // Check if editing existing employee
                const urlParams = new URLSearchParams(window.location.search);
                const employeeId = urlParams.get('id');

                if (employeeId) {
                    isEditMode = true;
                    currentEmployeeId = employeeId;
                    await loadEmployeeForEdit(employeeId);
                }

                // Setup form event listeners
                setupFormEventListeners();

                console.log('Employee form initialized successfully');
            } catch (error) {
                console.error('Error initializing employee form:', error);
                showError('خطأ في تحميل النموذج');
            }
        }

        // Load form options
        function loadFormOptions() {
            // Load ranks
            const rankSelect = document.getElementById('rank');
            CONSTANTS.MILITARY_RANKS.forEach(rank => {
                const option = document.createElement('option');
                option.value = rank.value;
                option.textContent = rank.label;
                rankSelect.appendChild(option);
            });

            // Load units
            const unitSelect = document.getElementById('unit');
            CONSTANTS.MILITARY_UNITS.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.value;
                option.textContent = unit.label;
                unitSelect.appendChild(option);
            });
        }

        // Load employee for editing
        async function loadEmployeeForEdit(employeeId) {
            try {
                showLoading(true);

                const response = await apiService.getEmployee(employeeId);

                if (response && response.success) {
                    const employee = response.data.employee;
                    populateForm(employee);
                    updateFormForEditMode();
                } else {
                    showError('لم يتم العثور على بيانات الموظف');
                    setTimeout(() => {
                        window.location.href = 'employees.html';
                    }, 2000);
                }
            } catch (error) {
                console.error('Error loading employee:', error);
                showError('خطأ في تحميل بيانات الموظف');
            } finally {
                showLoading(false);
            }
        }

        // Populate form with employee data
        function populateForm(employee) {
            // Basic information
            document.getElementById('employeeId').value = employee.id || '';
            document.getElementById('fullName').value = employee.fullName || '';
            document.getElementById('militaryNumber').value = employee.militaryNumber || '';
            document.getElementById('nationalId').value = employee.nationalId || '';
            document.getElementById('birthDate').value = employee.birthDate || '';
            document.getElementById('gender').value = employee.gender || '';
            document.getElementById('bloodType').value = employee.bloodType || '';
            document.getElementById('motherName').value = employee.motherName || '';
            document.getElementById('maritalStatus').value = employee.maritalStatus || '';

            // Contact information
            document.getElementById('phone').value = employee.phone || '';
            document.getElementById('email').value = employee.email || '';
            document.getElementById('address').value = employee.address || '';

            // Military information
            document.getElementById('rank').value = employee.rank || '';
            document.getElementById('unit').value = employee.unit || '';
            document.getElementById('position').value = employee.position || '';
            document.getElementById('appointmentDate').value = employee.appointmentDate || '';
            document.getElementById('serviceYears').value = employee.serviceYears || '';
            document.getElementById('status').value = employee.status || '';

            // Photo
            if (employee.photo) {
                displayPhoto(employee.photo);
            }

            // Calculate service years
            calculateServiceYears();
        }

        // Update form for edit mode
        function updateFormForEditMode() {
            document.getElementById('formTitle').innerHTML = '<i class="fas fa-user-edit"></i> تعديل بيانات الموظف';
            document.getElementById('formDescription').textContent = 'تعديل البيانات الموجودة للموظف';
            document.getElementById('breadcrumbTitle').textContent = 'تعديل موظف';
            document.getElementById('saveButtonText').textContent = 'حفظ التعديلات';
        }

        // Setup form event listeners
        function setupFormEventListeners() {
            // Form submission
            document.getElementById('employeeForm').addEventListener('submit', handleFormSubmit);

            // Appointment date change
            document.getElementById('appointmentDate').addEventListener('change', calculateServiceYears);

            // Military number validation
            document.getElementById('militaryNumber').addEventListener('blur', validateMilitaryNumber);

            // National ID validation
            document.getElementById('nationalId').addEventListener('blur', validateNationalId);
        }

        // Handle form submission
        async function handleFormSubmit(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }

            try {
                showLoading(true);

                const formData = collectFormData();
                let response;

                if (isEditMode) {
                    response = await apiService.updateEmployee(currentEmployeeId, formData);
                } else {
                    response = await apiService.createEmployee(formData);
                }

                if (response && response.success) {
                    showSuccess(isEditMode ? 'تم تحديث بيانات الموظف بنجاح' : 'تم إضافة الموظف بنجاح');

                    setTimeout(() => {
                        window.location.href = 'employees.html';
                    }, 2000);
                } else {
                    showError(response?.message || 'فشل في حفظ بيانات الموظف');
                }
            } catch (error) {
                console.error('Error saving employee:', error);
                showError('خطأ في حفظ بيانات الموظف');
            } finally {
                showLoading(false);
            }
        }

        // Collect form data
        function collectFormData() {
            return {
                fullName: document.getElementById('fullName').value.trim(),
                militaryNumber: document.getElementById('militaryNumber').value.trim(),
                nationalId: document.getElementById('nationalId').value.trim(),
                birthDate: document.getElementById('birthDate').value,
                gender: document.getElementById('gender').value,
                bloodType: document.getElementById('bloodType').value,
                motherName: document.getElementById('motherName').value.trim(),
                maritalStatus: document.getElementById('maritalStatus').value,
                phone: document.getElementById('phone').value.trim(),
                email: document.getElementById('email').value.trim(),
                address: document.getElementById('address').value.trim(),
                rank: document.getElementById('rank').value,
                unit: document.getElementById('unit').value,
                position: document.getElementById('position').value.trim(),
                appointmentDate: document.getElementById('appointmentDate').value,
                status: document.getElementById('status').value,
                photo: document.getElementById('photoPreview').src || null
            };
        }

        // Validate form
        function validateForm() {
            const requiredFields = [
                'fullName', 'militaryNumber', 'nationalId', 'birthDate',
                'gender', 'motherName', 'phone', 'rank', 'unit',
                'position', 'appointmentDate', 'status'
            ];

            let isValid = true;

            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            if (!isValid) {
                showError('يرجى ملء جميع الحقول المطلوبة');
            }

            return isValid;
        }

        // Calculate service years
        function calculateServiceYears() {
            const appointmentDate = document.getElementById('appointmentDate').value;
            if (appointmentDate) {
                const appointment = new Date(appointmentDate);
                const today = new Date();
                const years = Math.floor((today - appointment) / (365.25 * 24 * 60 * 60 * 1000));
                document.getElementById('serviceYears').value = Math.max(0, years);
            }
        }

        // Handle photo upload
        function handlePhotoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 2 * 1024 * 1024) {
                    showError('حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    displayPhoto(e.target.result);
                };
                reader.readAsDataURL(file);
            }
        }

        // Display photo
        function displayPhoto(src) {
            const preview = document.getElementById('photoPreview');
            preview.innerHTML = `<img src="${src}" alt="صورة الموظف" class="photo-preview">`;
        }

        // Validate military number
        async function validateMilitaryNumber() {
            const militaryNumber = document.getElementById('militaryNumber').value.trim();
            if (militaryNumber && (!isEditMode || militaryNumber !== originalMilitaryNumber)) {
                // Check if military number already exists
                // This would typically be an API call
                console.log('Validating military number:', militaryNumber);
            }
        }

        // Validate national ID
        function validateNationalId() {
            const nationalId = document.getElementById('nationalId').value.trim();
            if (nationalId) {
                // Validate national ID format
                if (!/^\d{10}$/.test(nationalId)) {
                    showError('رقم الهوية الوطنية يجب أن يكون 10 أرقام');
                    document.getElementById('nationalId').classList.add('error');
                } else {
                    document.getElementById('nationalId').classList.remove('error');
                }
            }
        }

        // Reset form
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                document.getElementById('employeeForm').reset();
                document.getElementById('photoPreview').innerHTML = '<i class="fas fa-camera"></i>';

                if (isEditMode && currentEmployeeId) {
                    loadEmployeeForEdit(currentEmployeeId);
                }
            }
        }

        // Cancel form
        function cancelForm() {
            if (confirm('هل أنت متأكد من الإلغاء؟ سيتم فقدان جميع البيانات المدخلة.')) {
                window.location.href = 'employees.html';
            }
        }
    </script>
</body>
</html>

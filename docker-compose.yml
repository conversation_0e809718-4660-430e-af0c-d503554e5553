version: '3.8'

services:
  # Database Services
  mongodb:
    image: mongo:7.0
    container_name: employee_mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: employee_management
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - employee_network

  redis:
    image: redis:7.2-alpine
    container_name: employee_redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - employee_network

  # Microservices
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: auth_service
    restart: always
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=******************************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-here
    depends_on:
      - mongodb
      - redis
    networks:
      - employee_network

  employee-service:
    build:
      context: ./services/employee-service
      dockerfile: Dockerfile
    container_name: employee_service
    restart: always
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=******************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - employee_network

  report-service:
    build:
      context: ./services/report-service
      dockerfile: Dockerfile
    container_name: report_service
    restart: always
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=******************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - employee_network

  barcode-service:
    build:
      context: ./services/barcode-service
      dockerfile: Dockerfile
    container_name: barcode_service
    restart: always
    ports:
      - "3004:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=******************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - employee_network

  file-service:
    build:
      context: ./services/file-service
      dockerfile: Dockerfile
    container_name: file_service
    restart: always
    ports:
      - "3005:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=******************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    volumes:
      - file_uploads:/app/uploads
    networks:
      - employee_network

  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    container_name: api_gateway
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - AUTH_SERVICE_URL=http://auth-service:3000
      - EMPLOYEE_SERVICE_URL=http://employee-service:3000
      - REPORT_SERVICE_URL=http://report-service:3000
      - BARCODE_SERVICE_URL=http://barcode-service:3000
      - FILE_SERVICE_URL=http://file-service:3000
    depends_on:
      - auth-service
      - employee-service
      - report-service
      - barcode-service
      - file-service
    networks:
      - employee_network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: employee_frontend
    restart: always
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost:3000
    depends_on:
      - api-gateway
    networks:
      - employee_network

volumes:
  mongodb_data:
  redis_data:
  file_uploads:

networks:
  employee_network:
    driver: bridge

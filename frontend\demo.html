<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض التحسينات - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span>نظام إدارة الموظفين - عرض التحسينات</span>
            </div>
            <div class="nav-menu">
                <button class="btn btn-primary" onclick="showToast('success')">
                    <i class="fas fa-check"></i>
                    إشعار نجاح
                </button>
                <button class="btn btn-warning" onclick="showToast('warning')">
                    <i class="fas fa-exclamation-triangle"></i>
                    إشعار تحذير
                </button>
                <button class="btn btn-danger" onclick="showToast('error')">
                    <i class="fas fa-times"></i>
                    إشعار خطأ
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div>
                <h1>
                    <i class="fas fa-palette"></i>
                    عرض التحسينات البصرية
                </h1>
                <p>استعراض التحسينات الجديدة على التصميم والأيقونات</p>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card primary hover-lift">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>150</h3>
                    <p>إجمالي الموظفين</p>
                </div>
                <span class="status-indicator online"></span>
            </div>

            <div class="stat-card success hover-lift">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-content">
                    <h3>142</h3>
                    <p>حاضر اليوم</p>
                </div>
                <span class="status-indicator online"></span>
            </div>

            <div class="stat-card warning hover-lift">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>5</h3>
                    <p>متأخر</p>
                </div>
                <span class="status-indicator busy"></span>
            </div>

            <div class="stat-card danger hover-lift">
                <div class="stat-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-content">
                    <h3>3</h3>
                    <p>غائب</p>
                </div>
                <span class="status-indicator offline"></span>
            </div>
        </div>

        <!-- Enhanced Cards Grid -->
        <div class="dashboard-grid">
            <div class="dashboard-card card-animated">
                <div class="card-header gradient-bg" style="color: white;">
                    <h3>
                        <i class="fas fa-chart-line"></i>
                        الإحصائيات المتقدمة
                    </h3>
                </div>
                <div class="card-content">
                    <p>عرض الإحصائيات بتصميم محسن مع تأثيرات بصرية جذابة</p>
                    <div class="icon-wrapper">
                        <i class="fas fa-analytics"></i>
                    </div>
                </div>
            </div>

            <div class="dashboard-card card-animated card-gradient">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-bell"></i>
                        نظام الإشعارات
                    </h3>
                </div>
                <div class="card-content">
                    <p>نظام إشعارات محسن مع تأثيرات انتقالية سلسة</p>
                    <button class="btn btn-info btn-sm" onclick="showToast('info')">
                        <i class="fas fa-info-circle"></i>
                        اختبار الإشعار
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Buttons Showcase -->
        <div class="card card-highlight">
            <div class="card-header">
                <h3>
                    <i class="fas fa-mouse-pointer"></i>
                    الأزرار المحسنة
                </h3>
            </div>
            <div class="card-content">
                <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                    <button class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-check"></i>
                        موافق
                    </button>
                    <button class="btn btn-warning">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                    <button class="btn btn-info">
                        <i class="fas fa-info"></i>
                        معلومات
                    </button>
                </div>
                
                <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                    <button class="btn btn-primary btn-lg">
                        <i class="fas fa-plus"></i>
                        زر كبير
                    </button>
                    <button class="btn btn-secondary btn-sm">
                        <i class="fas fa-cog"></i>
                        زر صغير
                    </button>
                    <button class="btn btn-primary btn-icon-only">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <script>
        // Toast Notification System
        function showToast(type) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            const icons = {
                success: 'fas fa-check',
                error: 'fas fa-times',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            const titles = {
                success: 'نجح العمل!',
                error: 'حدث خطأ!',
                warning: 'تحذير!',
                info: 'معلومة'
            };
            
            const messages = {
                success: 'تم تنفيذ العملية بنجاح',
                error: 'فشل في تنفيذ العملية',
                warning: 'يرجى الانتباه لهذا التحذير',
                info: 'معلومة مفيدة للمستخدم'
            };
            
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="${icons[type]}"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${titles[type]}</div>
                    <div class="toast-message">${messages[type]}</div>
                </div>
                <button class="toast-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
                <div class="toast-progress"></div>
            `;
            
            container.appendChild(toast);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }
        
        // Show loading demo
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';
                showToast('success');
            }, 2000);
        }
        
        // Add animation classes on load
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card-animated');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animationDelay = `${index * 0.1}s`;
                }, 100);
            });
        });
    </script>
</body>
</html>

// Authentication functionality for Employee Management System

// Authentication state
let authState = {
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
    loginAttempts: 0,
    maxLoginAttempts: 5,
    lockoutTime: 15 * 60 * 1000 // 15 minutes
};

// Initialize authentication
function initializeAuth() {
    // Check if user is already logged in
    const token = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    const userData = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
    
    if (token && userData) {
        try {
            authState.token = token;
            authState.user = JSON.parse(userData);
            authState.isAuthenticated = true;
            
            // Verify token validity
            verifyToken();
        } catch (error) {
            console.error('Error parsing stored user data:', error);
            clearAuthData();
        }
    }
    
    // Check for lockout
    checkLockout();
}

// Login function
async function login(username, password, rememberMe = false) {
    try {
        // Check if account is locked
        if (isAccountLocked()) {
            const remainingTime = getRemainingLockoutTime();
            showError(`الحساب مقفل. المحاولة مرة أخرى خلال ${Math.ceil(remainingTime / 60000)} دقيقة`);
            return false;
        }
        
        // Validate input
        if (!username || !password) {
            showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return false;
        }
        
        showLoading(true);
        
        // Call login API
        const response = await apiService.login(username, password);
        
        if (response && response.success) {
            // Store authentication data
            authState.token = response.data.token;
            authState.refreshToken = response.data.refreshToken;
            authState.user = response.data.user;
            authState.isAuthenticated = true;
            authState.loginAttempts = 0; // Reset login attempts
            
            // Store in localStorage
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN, response.data.token);
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN, response.data.refreshToken);
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));
            
            if (rememberMe) {
                localStorage.setItem(APP_CONFIG.STORAGE_KEYS.REMEMBER_ME, 'true');
            }
            
            // Clear lockout data
            clearLockoutData();
            
            showSuccess('تم تسجيل الدخول بنجاح');
            
            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
            
            return true;
        } else {
            // Handle login failure
            authState.loginAttempts++;
            
            const remainingAttempts = authState.maxLoginAttempts - authState.loginAttempts;
            
            if (remainingAttempts > 0) {
                showError(`اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: ${remainingAttempts}`);
            } else {
                // Lock account
                lockAccount();
                showError('تم قفل الحساب بسبب المحاولات المتعددة الفاشلة');
            }
            
            return false;
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى');
        return false;
    } finally {
        showLoading(false);
    }
}

// Logout function
async function logout() {
    try {
        showLoading(true);
        
        // Call logout API if token exists
        if (authState.token) {
            try {
                await apiService.logout();
            } catch (error) {
                console.error('Logout API error:', error);
                // Continue with local logout even if API fails
            }
        }
        
        // Clear authentication data
        clearAuthData();
        
        showSuccess('تم تسجيل الخروج بنجاح');
        
        // Redirect to login page
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
        
    } catch (error) {
        console.error('Logout error:', error);
        // Force logout even if there's an error
        clearAuthData();
        window.location.href = 'login.html';
    } finally {
        showLoading(false);
    }
}

// Clear authentication data
function clearAuthData() {
    authState.isAuthenticated = false;
    authState.user = null;
    authState.token = null;
    authState.refreshToken = null;
    
    // Clear localStorage
    localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
    localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.REMEMBER_ME);
}

// Verify token validity
async function verifyToken() {
    try {
        const response = await apiService.verifyToken();
        
        if (!response || !response.success) {
            // Token is invalid, try to refresh
            await refreshAuthToken();
        }
    } catch (error) {
        console.error('Token verification error:', error);
        // Try to refresh token
        await refreshAuthToken();
    }
}

// Refresh authentication token
async function refreshAuthToken() {
    try {
        const refreshToken = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN);
        
        if (!refreshToken) {
            clearAuthData();
            redirectToLogin();
            return false;
        }
        
        const response = await apiService.refreshToken(refreshToken);
        
        if (response && response.success) {
            // Update tokens
            authState.token = response.data.token;
            authState.refreshToken = response.data.refreshToken;
            
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN, response.data.token);
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN, response.data.refreshToken);
            
            return true;
        } else {
            // Refresh failed, redirect to login
            clearAuthData();
            redirectToLogin();
            return false;
        }
    } catch (error) {
        console.error('Token refresh error:', error);
        clearAuthData();
        redirectToLogin();
        return false;
    }
}

// Check if account is locked
function isAccountLocked() {
    const lockoutTime = localStorage.getItem('lockoutTime');
    if (!lockoutTime) return false;
    
    const lockoutTimestamp = parseInt(lockoutTime);
    const currentTime = Date.now();
    
    return currentTime < lockoutTimestamp;
}

// Get remaining lockout time
function getRemainingLockoutTime() {
    const lockoutTime = localStorage.getItem('lockoutTime');
    if (!lockoutTime) return 0;
    
    const lockoutTimestamp = parseInt(lockoutTime);
    const currentTime = Date.now();
    
    return Math.max(0, lockoutTimestamp - currentTime);
}

// Lock account
function lockAccount() {
    const lockoutTimestamp = Date.now() + authState.lockoutTime;
    localStorage.setItem('lockoutTime', lockoutTimestamp.toString());
    localStorage.setItem('loginAttempts', authState.loginAttempts.toString());
}

// Clear lockout data
function clearLockoutData() {
    localStorage.removeItem('lockoutTime');
    localStorage.removeItem('loginAttempts');
}

// Check lockout status
function checkLockout() {
    const storedAttempts = localStorage.getItem('loginAttempts');
    if (storedAttempts) {
        authState.loginAttempts = parseInt(storedAttempts);
    }
    
    if (isAccountLocked()) {
        const remainingTime = getRemainingLockoutTime();
        console.log(`Account locked for ${Math.ceil(remainingTime / 60000)} minutes`);
    }
}

// Redirect to login page
function redirectToLogin() {
    if (window.location.pathname !== '/login.html') {
        window.location.href = 'login.html';
    }
}

// Check authentication status
function isAuthenticated() {
    return authState.isAuthenticated && authState.token;
}

// Get current user
function getCurrentUser() {
    return authState.user;
}

// Get authentication token
function getAuthToken() {
    return authState.token;
}

// Change password
async function changePassword(currentPassword, newPassword, confirmPassword) {
    try {
        // Validate input
        if (!currentPassword || !newPassword || !confirmPassword) {
            showError('يرجى ملء جميع الحقول');
            return false;
        }
        
        if (newPassword !== confirmPassword) {
            showError('كلمة المرور الجديدة وتأكيدها غير متطابقتين');
            return false;
        }
        
        if (newPassword.length < 8) {
            showError('كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل');
            return false;
        }
        
        showLoading(true);
        
        const response = await apiService.changePassword(currentPassword, newPassword);
        
        if (response && response.success) {
            showSuccess('تم تغيير كلمة المرور بنجاح');
            return true;
        } else {
            showError(response?.message || 'فشل في تغيير كلمة المرور');
            return false;
        }
    } catch (error) {
        console.error('Change password error:', error);
        showError('خطأ في تغيير كلمة المرور');
        return false;
    } finally {
        showLoading(false);
    }
}

// Reset password
async function resetPassword(email) {
    try {
        if (!email) {
            showError('يرجى إدخال البريد الإلكتروني');
            return false;
        }
        
        if (!UTILS.isValidEmail(email)) {
            showError('البريد الإلكتروني غير صحيح');
            return false;
        }
        
        showLoading(true);
        
        const response = await apiService.resetPassword(email);
        
        if (response && response.success) {
            showSuccess('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
            return true;
        } else {
            showError(response?.message || 'فشل في إرسال رابط إعادة التعيين');
            return false;
        }
    } catch (error) {
        console.error('Reset password error:', error);
        showError('خطأ في إعادة تعيين كلمة المرور');
        return false;
    } finally {
        showLoading(false);
    }
}

// Update user profile
async function updateProfile(profileData) {
    try {
        showLoading(true);
        
        const response = await apiService.updateProfile(profileData);
        
        if (response && response.success) {
            // Update local user data
            authState.user = { ...authState.user, ...response.data.user };
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(authState.user));
            
            showSuccess('تم تحديث الملف الشخصي بنجاح');
            return true;
        } else {
            showError(response?.message || 'فشل في تحديث الملف الشخصي');
            return false;
        }
    } catch (error) {
        console.error('Update profile error:', error);
        showError('خطأ في تحديث الملف الشخصي');
        return false;
    } finally {
        showLoading(false);
    }
}

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

// Auto-refresh token before expiration
setInterval(async () => {
    if (authState.isAuthenticated && authState.token) {
        try {
            await verifyToken();
        } catch (error) {
            console.error('Auto token refresh error:', error);
        }
    }
}, 10 * 60 * 1000); // Check every 10 minutes

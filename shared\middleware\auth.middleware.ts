import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';
import { User, Permission } from '../types/user.types';

export interface AuthenticatedRequest extends Request {
  user?: User;
  permissions?: Permission[];
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const token = this.extractTokenFromHeader(req);
      
      if (!token) {
        throw new UnauthorizedException('Access token is required');
      }

      const payload = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any;
      
      if (!payload || !payload.sub) {
        throw new UnauthorizedException('Invalid token');
      }

      // Attach user information to request
      req.user = {
        _id: payload.sub,
        username: payload.username,
        email: payload.email,
        role: payload.role,
        permissions: payload.permissions || [],
        profile: payload.profile || {},
        isActive: payload.isActive,
        createdAt: payload.createdAt,
        updatedAt: payload.updatedAt,
        password: '', // Never expose password
        loginAttempts: 0
      };

      req.permissions = payload.permissions || [];

      next();
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new UnauthorizedException('Invalid token');
      }
      if (error instanceof jwt.TokenExpiredError) {
        throw new UnauthorizedException('Token has expired');
      }
      throw new UnauthorizedException('Authentication failed');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}

// Permission Guard Decorator
export function RequirePermissions(...permissions: Permission[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const req = args.find(arg => arg && arg.user && arg.permissions);
      
      if (!req) {
        throw new UnauthorizedException('Authentication required');
      }

      const userPermissions = req.permissions || [];
      const hasPermission = permissions.some(permission => 
        userPermissions.includes(permission) || 
        userPermissions.includes(Permission.READ_ALL) ||
        userPermissions.includes(Permission.WRITE_ALL)
      );

      if (!hasPermission) {
        throw new UnauthorizedException(`Required permissions: ${permissions.join(', ')}`);
      }

      return method.apply(this, args);
    };
  };
}

// Role Guard Decorator
export function RequireRoles(...roles: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const req = args.find(arg => arg && arg.user);
      
      if (!req || !req.user) {
        throw new UnauthorizedException('Authentication required');
      }

      const userRole = req.user.role;
      const hasRole = roles.includes(userRole);

      if (!hasRole) {
        throw new UnauthorizedException(`Required roles: ${roles.join(', ')}`);
      }

      return method.apply(this, args);
    };
  };
}

// Utility functions for permission checking
export class PermissionHelper {
  static hasPermission(userPermissions: Permission[], requiredPermission: Permission): boolean {
    return userPermissions.includes(requiredPermission) ||
           userPermissions.includes(Permission.READ_ALL) ||
           userPermissions.includes(Permission.WRITE_ALL);
  }

  static hasAnyPermission(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
    return requiredPermissions.some(permission => this.hasPermission(userPermissions, permission));
  }

  static hasAllPermissions(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
    return requiredPermissions.every(permission => this.hasPermission(userPermissions, permission));
  }

  static canRead(userPermissions: Permission[], resource: string): boolean {
    const readPermissions = [
      Permission.READ_ALL,
      `READ_${resource.toUpperCase()}` as Permission
    ];
    return this.hasAnyPermission(userPermissions, readPermissions);
  }

  static canWrite(userPermissions: Permission[], resource: string): boolean {
    const writePermissions = [
      Permission.WRITE_ALL,
      `CREATE_${resource.toUpperCase()}` as Permission,
      `UPDATE_${resource.toUpperCase()}` as Permission
    ];
    return this.hasAnyPermission(userPermissions, writePermissions);
  }

  static canDelete(userPermissions: Permission[], resource: string): boolean {
    const deletePermissions = [
      Permission.DELETE_ALL,
      `DELETE_${resource.toUpperCase()}` as Permission
    ];
    return this.hasAnyPermission(userPermissions, deletePermissions);
  }
}

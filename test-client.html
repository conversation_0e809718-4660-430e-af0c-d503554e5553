<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - اختبار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.running {
            background-color: #d4edda;
            color: #155724;
        }
        .employee-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .employee-card h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام إدارة الموظفين</h1>
            <p>صفحة اختبار النظام</p>
            <div id="systemStatus"></div>
        </div>

        <!-- Login Section -->
        <div class="section">
            <h2>🔐 تسجيل الدخول</h2>
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="password" value="admin123">
            </div>
            <button class="btn" onclick="login()">تسجيل الدخول</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- Employees Section -->
        <div class="section">
            <h2>👥 إدارة الموظفين</h2>
            <button class="btn" onclick="getEmployees()">عرض الموظفين</button>
            <button class="btn btn-success" onclick="getStatistics()">الإحصائيات</button>
            <div id="employeesResult" class="result" style="display: none;"></div>
        </div>

        <!-- Add Employee Section -->
        <div class="section">
            <h2>➕ إضافة موظف جديد</h2>
            <div class="form-group">
                <label>الاسم الكامل:</label>
                <input type="text" id="fullName" placeholder="أدخل الاسم الكامل">
            </div>
            <div class="form-group">
                <label>الرقم العسكري:</label>
                <input type="text" id="militaryNumber" placeholder="M003">
            </div>
            <div class="form-group">
                <label>الرتبة:</label>
                <input type="text" id="rank" placeholder="جندي أول">
            </div>
            <div class="form-group">
                <label>الوحدة:</label>
                <input type="text" id="unit" placeholder="الوحدة الثالثة">
            </div>
            <button class="btn btn-success" onclick="addEmployee()">إضافة الموظف</button>
            <div id="addEmployeeResult" class="result" style="display: none;"></div>
        </div>

        <!-- Other Services Section -->
        <div class="section">
            <h2>📊 خدمات أخرى</h2>
            <button class="btn" onclick="getAttendance()">سجل الحضور</button>
            <button class="btn" onclick="getLeaves()">طلبات الإجازات</button>
            <button class="btn" onclick="generateReport()">إنشاء تقرير</button>
            <div id="servicesResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1';
        let authToken = null;

        // Check system status on load
        window.onload = function() {
            checkSystemStatus();
        };

        async function checkSystemStatus() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('systemStatus').innerHTML = `
                        <div style="color: green;">
                            ✅ النظام يعمل بنجاح
                            <br>
                            <span class="status running">المصادقة: ${data.services.auth}</span>
                            <span class="status running">الموظفين: ${data.services.employees}</span>
                            <span class="status running">التقارير: ${data.services.reports}</span>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('systemStatus').innerHTML = `
                    <div style="color: red;">❌ خطأ في الاتصال بالنظام</div>
                `;
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('loginResult');
                
                if (data.success) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ تم تسجيل الدخول بنجاح!\n\nمرحباً ${data.data.user.username}\nالدور: ${data.data.user.role}\nالصلاحيات: ${data.data.user.permissions.join(', ')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل تسجيل الدخول: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('loginResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function getEmployees() {
            try {
                const response = await fetch(`${API_BASE}/employees`);
                const data = await response.json();
                const resultDiv = document.getElementById('employeesResult');
                
                if (data.success) {
                    let html = `✅ تم جلب الموظفين بنجاح!\n\nإجمالي الموظفين: ${data.pagination.total}\n\n`;
                    
                    data.data.forEach(emp => {
                        html += `
                            <div class="employee-card">
                                <h4>${emp.personalInfo.fullName}</h4>
                                <p><strong>الرقم العسكري:</strong> ${emp.personalInfo.militaryNumber}</p>
                                <p><strong>الرتبة:</strong> ${emp.jobInfo.rank}</p>
                                <p><strong>الوحدة:</strong> ${emp.jobInfo.unit}</p>
                                <p><strong>المنصب:</strong> ${emp.jobInfo.position}</p>
                                <p><strong>الراتب:</strong> ${emp.jobInfo.salary} ريال</p>
                            </div>
                        `;
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل جلب الموظفين: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('employeesResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function getStatistics() {
            try {
                const response = await fetch(`${API_BASE}/employees/statistics`);
                const data = await response.json();
                const resultDiv = document.getElementById('employeesResult');
                
                if (data.success) {
                    const stats = data.data;
                    let html = `✅ إحصائيات الموظفين:\n\n`;
                    html += `📊 إجمالي الموظفين: ${stats.totalEmployees}\n`;
                    html += `✅ الموظفين النشطين: ${stats.activeEmployees}\n`;
                    html += `❌ الموظفين غير النشطين: ${stats.inactiveEmployees}\n\n`;
                    html += `📍 حسب الوحدة:\n`;
                    Object.entries(stats.byUnit).forEach(([unit, count]) => {
                        html += `   • ${unit}: ${count}\n`;
                    });
                    html += `\n🎖️ حسب الرتبة:\n`;
                    Object.entries(stats.byRank).forEach(([rank, count]) => {
                        html += `   • ${rank}: ${count}\n`;
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل جلب الإحصائيات: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('employeesResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function addEmployee() {
            const employeeData = {
                personalInfo: {
                    fullName: document.getElementById('fullName').value,
                    militaryNumber: document.getElementById('militaryNumber').value
                },
                jobInfo: {
                    rank: document.getElementById('rank').value,
                    unit: document.getElementById('unit').value
                }
            };
            
            try {
                const response = await fetch(`${API_BASE}/employees`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(employeeData)
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('addEmployeeResult');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ تم إضافة الموظف بنجاح!\n\nالاسم: ${data.data.personalInfo.fullName}\nالرقم العسكري: ${data.data.personalInfo.militaryNumber}`;
                    
                    // Clear form
                    document.getElementById('fullName').value = '';
                    document.getElementById('militaryNumber').value = '';
                    document.getElementById('rank').value = '';
                    document.getElementById('unit').value = '';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل إضافة الموظف: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('addEmployeeResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function getAttendance() {
            try {
                const response = await fetch(`${API_BASE}/attendance`);
                const data = await response.json();
                const resultDiv = document.getElementById('servicesResult');
                
                if (data.success) {
                    let html = `✅ سجل الحضور:\n\n`;
                    data.data.forEach(record => {
                        html += `📅 التاريخ: ${record.date}\n`;
                        html += `⏰ وقت الدخول: ${record.checkIn}\n`;
                        html += `⏰ وقت الخروج: ${record.checkOut}\n`;
                        html += `⏱️ ساعات العمل: ${record.workingHours}\n`;
                        html += `📊 الحالة: ${record.status}\n\n`;
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل جلب سجل الحضور: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('servicesResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function getLeaves() {
            try {
                const response = await fetch(`${API_BASE}/leaves`);
                const data = await response.json();
                const resultDiv = document.getElementById('servicesResult');
                
                if (data.success) {
                    let html = `✅ طلبات الإجازات:\n\n`;
                    data.data.forEach(leave => {
                        html += `📝 النوع: ${leave.type}\n`;
                        html += `📅 من: ${leave.startDate} إلى: ${leave.endDate}\n`;
                        html += `📊 عدد الأيام: ${leave.totalDays}\n`;
                        html += `✅ الحالة: ${leave.status}\n`;
                        html += `📄 السبب: ${leave.reason}\n\n`;
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل جلب طلبات الإجازات: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('servicesResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function generateReport() {
            try {
                const response = await fetch(`${API_BASE}/reports/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'EMPLOYEE_LIST',
                        format: 'PDF'
                    })
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('servicesResult');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ تم إنشاء التقرير بنجاح!\n\nمعرف التقرير: ${data.data.reportId}\nالنوع: ${data.data.type}\nالتنسيق: ${data.data.format}\nتاريخ الإنشاء: ${data.data.generatedAt}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل إنشاء التقرير: ${data.message}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('servicesResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاتصال: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }
    </script>
</body>
</html>

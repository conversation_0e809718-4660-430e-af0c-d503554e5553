export interface Employee {
  _id?: string;
  // البيانات الشخصية - Personal Information
  personalInfo: {
    fullName: string;
    fullNameEn?: string;
    nationalId: string;
    militaryNumber: string;
    motherName: string;
    bloodType: BloodType;
    photo?: string;
    dateOfBirth: Date;
    placeOfBirth: string;
    nationality: string;
    maritalStatus: MaritalStatus;
    phoneNumber: string;
    emergencyContact: {
      name: string;
      relationship: string;
      phone: string;
    };
    address: {
      street: string;
      city: string;
      province: string;
      postalCode?: string;
    };
  };

  // البيانات الوظيفية - Job Information
  jobInfo: {
    rank: string;
    unit: string;
    position: string;
    location: string;
    appointmentDate: Date;
    lastPromotionDate?: Date;
    jobStatus: JobStatus;
    salary: number;
    bankInfo: {
      bankName: string;
      accountNumber: string;
      iban?: string;
    };
  };

  // البيانات الإدارية - Administrative Information
  administrativeInfo: {
    leaveBalance: number;
    totalLeavesTaken: number;
    punishments: Punishment[];
    rewards: Reward[];
    medicalInfo?: {
      medicalConditions?: string[];
      allergies?: string[];
      medications?: string[];
    };
  };

  // معلومات النظام - System Information
  systemInfo: {
    barcode: string;
    qrCode?: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface CreateEmployeeDto {
  personalInfo: Omit<Employee['personalInfo'], 'photo'>;
  jobInfo: Employee['jobInfo'];
  administrativeInfo?: Partial<Employee['administrativeInfo']>;
}

export interface UpdateEmployeeDto {
  personalInfo?: Partial<Employee['personalInfo']>;
  jobInfo?: Partial<Employee['jobInfo']>;
  administrativeInfo?: Partial<Employee['administrativeInfo']>;
}

export interface Punishment {
  _id?: string;
  type: PunishmentType;
  reason: string;
  description: string;
  date: Date;
  duration?: number; // in days
  issuedBy: string;
  status: PunishmentStatus;
  documents?: string[];
}

export interface Reward {
  _id?: string;
  type: RewardType;
  reason: string;
  description: string;
  date: Date;
  amount?: number;
  issuedBy: string;
  documents?: string[];
}

export interface Leave {
  _id?: string;
  employeeId: string;
  type: LeaveType;
  startDate: Date;
  endDate: Date;
  duration: number; // in days
  reason: string;
  status: LeaveStatus;
  approvedBy?: string;
  approvalDate?: Date;
  documents?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Attendance {
  _id?: string;
  employeeId: string;
  date: Date;
  checkIn?: Date;
  checkOut?: Date;
  status: AttendanceStatus;
  notes?: string;
  location?: string;
  method: AttendanceMethod; // barcode, manual, biometric
}

// Enums
export enum BloodType {
  A_POSITIVE = 'A+',
  A_NEGATIVE = 'A-',
  B_POSITIVE = 'B+',
  B_NEGATIVE = 'B-',
  AB_POSITIVE = 'AB+',
  AB_NEGATIVE = 'AB-',
  O_POSITIVE = 'O+',
  O_NEGATIVE = 'O-'
}

export enum MaritalStatus {
  SINGLE = 'SINGLE',
  MARRIED = 'MARRIED',
  DIVORCED = 'DIVORCED',
  WIDOWED = 'WIDOWED'
}

export enum JobStatus {
  ACTIVE = 'ACTIVE',
  ON_LEAVE = 'ON_LEAVE',
  SUSPENDED = 'SUSPENDED',
  RETIRED = 'RETIRED',
  TERMINATED = 'TERMINATED'
}

export enum PunishmentType {
  WARNING = 'WARNING',
  REPRIMAND = 'REPRIMAND',
  SUSPENSION = 'SUSPENSION',
  DEMOTION = 'DEMOTION',
  FINE = 'FINE'
}

export enum PunishmentStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum RewardType {
  COMMENDATION = 'COMMENDATION',
  MEDAL = 'MEDAL',
  BONUS = 'BONUS',
  PROMOTION = 'PROMOTION',
  CERTIFICATE = 'CERTIFICATE'
}

export enum LeaveType {
  ANNUAL = 'ANNUAL',
  SICK = 'SICK',
  EMERGENCY = 'EMERGENCY',
  MATERNITY = 'MATERNITY',
  PATERNITY = 'PATERNITY',
  STUDY = 'STUDY',
  PILGRIMAGE = 'PILGRIMAGE'
}

export enum LeaveStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED'
}

export enum AttendanceStatus {
  PRESENT = 'PRESENT',
  ABSENT = 'ABSENT',
  LATE = 'LATE',
  EARLY_LEAVE = 'EARLY_LEAVE',
  ON_LEAVE = 'ON_LEAVE'
}

export enum AttendanceMethod {
  BARCODE = 'BARCODE',
  MANUAL = 'MANUAL',
  BIOMETRIC = 'BIOMETRIC',
  QR_CODE = 'QR_CODE'
}

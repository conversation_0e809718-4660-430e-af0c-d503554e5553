// API Service Layer for Employee Management System

class APIService {
    constructor() {
        this.baseURL = API_CONFIG.BASE_URL;
        this.token = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        this.refreshToken = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN);
    }

    // Set authentication token
    setToken(token) {
        this.token = token;
        localStorage.setItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN, token);
    }

    // Set refresh token
    setRefreshToken(refreshToken) {
        this.refreshToken = refreshToken;
        localStorage.setItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    }

    // Clear authentication tokens
    clearTokens() {
        this.token = null;
        this.refreshToken = null;
        localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
    }

    // Get default headers
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        if (includeAuth && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    // Make HTTP request
    async makeRequest(url, options = {}) {
        const config = {
            method: 'GET',
            headers: this.getHeaders(options.includeAuth !== false),
            ...options
        };

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            
            // Handle 401 Unauthorized - try to refresh token
            if (response.status === 401 && this.refreshToken) {
                const refreshed = await this.refreshAuthToken();
                if (refreshed) {
                    // Retry the original request with new token
                    config.headers = this.getHeaders(options.includeAuth !== false);
                    const retryResponse = await fetch(`${this.baseURL}${url}`, config);
                    return await this.handleResponse(retryResponse);
                } else {
                    // Refresh failed, redirect to login
                    this.handleAuthFailure();
                    throw new Error('Authentication failed');
                }
            }

            return await this.handleResponse(response);
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // Handle API response
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP Error: ${response.status}`);
            }
            
            return data;
        } else {
            if (!response.ok) {
                throw new Error(`HTTP Error: ${response.status}`);
            }
            
            return response;
        }
    }

    // Refresh authentication token
    async refreshAuthToken() {
        try {
            const response = await fetch(`${this.baseURL}${API_CONFIG.ENDPOINTS.REFRESH}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refreshToken: this.refreshToken
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.setToken(data.data.token);
                    this.setRefreshToken(data.data.refreshToken);
                    return true;
                }
            }
            
            return false;
        } catch (error) {
            console.error('Token refresh error:', error);
            return false;
        }
    }

    // Handle authentication failure
    handleAuthFailure() {
        this.clearTokens();
        if (window.location.pathname !== '/login.html') {
            window.location.href = 'login.html';
        }
    }

    // Authentication API calls
    async login(username, password) {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.LOGIN, {
            method: 'POST',
            body: JSON.stringify({ username, password }),
            includeAuth: false
        });
    }

    async logout() {
        try {
            await this.makeRequest(API_CONFIG.ENDPOINTS.LOGOUT, {
                method: 'POST'
            });
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.clearTokens();
        }
    }

    // Employee API calls
    async getEmployees(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${API_CONFIG.ENDPOINTS.EMPLOYEES}?${queryString}` : API_CONFIG.ENDPOINTS.EMPLOYEES;
        return await this.makeRequest(url);
    }

    async getEmployee(id) {
        return await this.makeRequest(`${API_CONFIG.ENDPOINTS.EMPLOYEES}/${id}`);
    }

    async createEmployee(employeeData) {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.EMPLOYEES, {
            method: 'POST',
            body: JSON.stringify(employeeData)
        });
    }

    async updateEmployee(id, employeeData) {
        return await this.makeRequest(`${API_CONFIG.ENDPOINTS.EMPLOYEES}/${id}`, {
            method: 'PUT',
            body: JSON.stringify(employeeData)
        });
    }

    async deleteEmployee(id) {
        return await this.makeRequest(`${API_CONFIG.ENDPOINTS.EMPLOYEES}/${id}`, {
            method: 'DELETE'
        });
    }

    async getEmployeeStatistics() {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.EMPLOYEE_STATISTICS);
    }

    // Attendance API calls
    async getAttendance(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${API_CONFIG.ENDPOINTS.ATTENDANCE}?${queryString}` : API_CONFIG.ENDPOINTS.ATTENDANCE;
        return await this.makeRequest(url);
    }

    async checkIn(employeeId, notes = '') {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.CHECK_IN, {
            method: 'POST',
            body: JSON.stringify({ employeeId, notes })
        });
    }

    async checkOut(employeeId, notes = '') {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.CHECK_OUT, {
            method: 'POST',
            body: JSON.stringify({ employeeId, notes })
        });
    }

    // Leave API calls
    async getLeaves(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${API_CONFIG.ENDPOINTS.LEAVES}?${queryString}` : API_CONFIG.ENDPOINTS.LEAVES;
        return await this.makeRequest(url);
    }

    async createLeave(leaveData) {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.LEAVES, {
            method: 'POST',
            body: JSON.stringify(leaveData)
        });
    }

    async approveLeave(id) {
        const url = API_CONFIG.ENDPOINTS.APPROVE_LEAVE.replace('{id}', id);
        return await this.makeRequest(url, {
            method: 'POST'
        });
    }

    async rejectLeave(id, reason = '') {
        const url = API_CONFIG.ENDPOINTS.REJECT_LEAVE.replace('{id}', id);
        return await this.makeRequest(url, {
            method: 'POST',
            body: JSON.stringify({ reason })
        });
    }

    // Report API calls
    async generateReport(type, params = {}) {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.REPORTS, {
            method: 'POST',
            body: JSON.stringify({ type, params })
        });
    }

    async downloadReport(id) {
        const url = API_CONFIG.ENDPOINTS.DOWNLOAD_REPORT.replace('{id}', id);
        const response = await this.makeRequest(url);
        return response;
    }

    // Health check
    async checkHealth() {
        return await this.makeRequest(API_CONFIG.ENDPOINTS.HEALTH, {
            includeAuth: false
        });
    }

    // File upload helper
    async uploadFile(file, endpoint) {
        const formData = new FormData();
        formData.append('file', file);

        const headers = {};
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'POST',
                headers: headers,
                body: formData
            });

            return await this.handleResponse(response);
        } catch (error) {
            console.error('File upload error:', error);
            throw error;
        }
    }
}

// Create global API service instance
const apiService = new APIService();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIService;
}

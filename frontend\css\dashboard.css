/* Dashboard Styles */

/* Navigation Bar */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-800);
}

.nav-brand i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    background: var(--gray-50);
}

.nav-link.active {
    color: var(--primary-color);
    background: rgb(37 99 235 / 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--gray-700);
    font-weight: 500;
}

.user-info i {
    font-size: var(--font-size-xl);
    color: var(--gray-400);
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: var(--danger-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.logout-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
}

/* Page Styles */
.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.page-header h1 {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin: 0;
    color: var(--gray-800);
}

.page-header h1 i {
    color: var(--primary-color);
}

.page-header p {
    color: var(--gray-600);
    margin: var(--spacing-2) 0 0 0;
    font-size: var(--font-size-sm);
}

.header-actions {
    display: flex;
    gap: var(--spacing-3);
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--white) 0%, #fafbfc 100%);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.stat-card.primary::before { background: var(--primary-color); }
.stat-card.success::before { background: var(--success-color); }
.stat-card.warning::before { background: var(--warning-color); }
.stat-card.info::before { background: var(--info-color); }
.stat-card.danger::before { background: var(--danger-color); }

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
}

.stat-card.primary .stat-icon { background: var(--primary-color); }
.stat-card.success .stat-icon { background: var(--success-color); }
.stat-card.warning .stat-icon { background: var(--warning-color); }
.stat-card.info .stat-icon { background: var(--info-color); }
.stat-card.danger .stat-icon { background: var(--danger-color); }

.stat-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 var(--spacing-1) 0;
}

.stat-content p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin: 0;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.dashboard-card .card-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.dashboard-card .card-header h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--gray-800);
}

.dashboard-card .card-header h3 i {
    color: var(--primary-color);
}

.dashboard-card .card-content {
    padding: var(--spacing-6);
}

/* Chart Container */
.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    color: var(--gray-500);
    font-style: italic;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--gray-100);
    transition: background var(--transition-fast);
}

.activity-item:hover {
    background: var(--gray-50);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--white);
    flex-shrink: 0;
}

.activity-icon.success { background: var(--success-color); }
.activity-icon.warning { background: var(--warning-color); }
.activity-icon.info { background: var(--info-color); }
.activity-icon.danger { background: var(--danger-color); }

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 var(--spacing-1) 0;
}

.activity-content p {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin: 0;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    flex-shrink: 0;
}

/* Quick Actions */
.quick-actions {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.quick-actions h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    color: var(--gray-800);
}

.quick-actions h3 i {
    color: var(--warning-color);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-fast);
    cursor: pointer;
    background: none;
}

.action-btn.primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.action-btn.primary:hover {
    background: var(--primary-color);
    color: var(--white);
}

.action-btn.success {
    color: var(--success-color);
    border-color: var(--success-color);
}

.action-btn.success:hover {
    background: var(--success-color);
    color: var(--white);
}

.action-btn.warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
}

.action-btn.warning:hover {
    background: var(--warning-color);
    color: var(--white);
}

.action-btn.info {
    color: var(--info-color);
    border-color: var(--info-color);
}

.action-btn.info:hover {
    background: var(--info-color);
    color: var(--white);
}

/* Filters Section */
.filters-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    right: var(--spacing-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-12) var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
}

.filter-group {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.filter-group select {
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    min-width: 150px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-6);
}

.pagination button {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-700);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.pagination button:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        display: none;
    }
    
    .nav-container {
        padding: 0 var(--spacing-4);
    }
    
    .main-content {
        padding: var(--spacing-6) var(--spacing-4);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-4);
    }
    
    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-group {
        justify-content: stretch;
    }
    
    .filter-group select {
        flex: 1;
        min-width: auto;
    }
    
    .nav-user {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .user-info {
        font-size: var(--font-size-sm);
    }
    
    .logout-btn {
        padding: var(--spacing-1) var(--spacing-3);
        font-size: var(--font-size-xs);
    }
}

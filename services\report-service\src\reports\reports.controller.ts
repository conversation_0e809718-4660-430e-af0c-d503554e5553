import {
  <PERSON>,
  Post,
  Body,
  Res,
  HttpStatus,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import { ReportsService, GenerateReportDto } from './reports.service';
import { Permission } from '@shared/types/employee.types';
import { RequirePermissions } from '@shared/middleware/auth.middleware';
import { AuthenticatedRequest } from '@shared/middleware/auth.middleware';

@Controller('reports')
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Post('generate')
  @RequirePermissions(Permission.GENERATE_REPORTS)
  async generateReport(
    @Body() generateReportDto: GenerateReportDto,
    @Req() req: AuthenticatedRequest,
    @Res() res: Response
  ) {
    try {
      const reportBuffer = await this.reportsService.generateReport(
        generateReportDto,
        req.user._id
      );

      // Set appropriate headers based on format
      const filename = `report_${Date.now()}`;
      let contentType: string;
      let fileExtension: string;

      switch (generateReportDto.format) {
        case 'PDF':
          contentType = 'application/pdf';
          fileExtension = 'pdf';
          break;
        case 'EXCEL':
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          fileExtension = 'xlsx';
          break;
        case 'WORD':
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          fileExtension = 'docx';
          break;
        default:
          contentType = 'application/octet-stream';
          fileExtension = 'bin';
      }

      res.set({
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}.${fileExtension}"`,
        'Content-Length': reportBuffer.length,
      });

      res.status(HttpStatus.OK).send(reportBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to generate report',
        error: error.message,
      });
    }
  }
}

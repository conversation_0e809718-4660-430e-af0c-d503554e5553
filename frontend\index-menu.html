<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فهرس النظام - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .menu-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
        }
        
        .menu-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .menu-header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .menu-header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .menu-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .menu-item:hover::before {
            transform: scaleX(1);
        }
        
        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }
        
        .menu-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .menu-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .menu-description {
            color: #7f8c8d;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .system-info {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-top: 30px;
        }
        
        .system-info h3 {
            color: #3498db;
            margin-bottom: 10px;
        }
        
        .credentials {
            background: rgba(46, 204, 113, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .credentials strong {
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="menu-container">
        <div class="menu-header">
            <h1>
                <i class="fas fa-building"></i>
                نظام إدارة الموظفين
            </h1>
            <p>نظام شامل لإدارة الموظفين العسكريين مع دعم كامل للغة العربية</p>
        </div>
        
        <div class="menu-grid">
            <a href="login.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <div class="menu-title">تسجيل الدخول</div>
                <div class="menu-description">صفحة تسجيل الدخول الآمنة للنظام</div>
            </a>
            
            <a href="index.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="menu-title">لوحة التحكم</div>
                <div class="menu-description">الصفحة الرئيسية مع الإحصائيات والمعلومات العامة</div>
            </a>
            
            <a href="employees.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="menu-title">إدارة الموظفين</div>
                <div class="menu-description">عرض وإدارة بيانات الموظفين والبحث والتصفية</div>
            </a>
            
            <a href="employee-form.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="menu-title">إضافة موظف</div>
                <div class="menu-description">نموذج إضافة موظف جديد مع جميع البيانات المطلوبة</div>
            </a>
            
            <a href="attendance.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="menu-title">إدارة الحضور</div>
                <div class="menu-description">تسجيل الحضور والانصراف ومتابعة الحضور اليومي</div>
            </a>
            
            <a href="leaves.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="menu-title">إدارة الإجازات</div>
                <div class="menu-description">طلب الإجازات وإدارة الطلبات ومتابعة الرصيد</div>
            </a>
            
            <a href="reports.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="menu-title">التقارير</div>
                <div class="menu-description">إنشاء وعرض التقارير المختلفة والإحصائيات</div>
            </a>
            
            <a href="demo.html" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="menu-title">عرض التحسينات</div>
                <div class="menu-description">استعراض التحسينات البصرية والميزات الجديدة</div>
            </a>
        </div>
        
        <div class="system-info">
            <h3>
                <i class="fas fa-info-circle"></i>
                معلومات النظام
            </h3>
            <p>النظام يعمل على الخادم المحلي ويدعم جميع الميزات المطلوبة</p>
            
            <div class="credentials">
                <strong>بيانات تسجيل الدخول للاختبار:</strong><br>
                اسم المستخدم: <strong>admin</strong><br>
                كلمة المرور: <strong>admin123</strong>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');
            
            menuItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .menu-item {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

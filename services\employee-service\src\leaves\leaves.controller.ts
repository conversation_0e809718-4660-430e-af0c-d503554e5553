import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { 
  LeavesService, 
  CreateLeaveDto, 
  UpdateLeaveDto, 
  ApproveLeaveDto, 
  RejectLeaveDto 
} from './leaves.service';
import { Permission } from '@shared/types/employee.types';
import { RequirePermissions } from '@shared/middleware/auth.middleware';
import { AuthenticatedRequest } from '@shared/middleware/auth.middleware';

@Controller('leaves')
export class LeavesController {
  constructor(private readonly leavesService: LeavesService) {}

  @Post()
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async create(
    @Body() createLeaveDto: CreateLeaveDto,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Leave request created successfully',
      data: await this.leavesService.create(createLeaveDto, req.user._id)
    };
  }

  @Get()
  @RequirePermissions(Permission.READ_LEAVES)
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('employeeId') employeeId?: string,
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('isEmergency') isEmergency?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('approvedBy') approvedBy?: string,
    @Query('requestedBy') requestedBy?: string,
  ) {
    const filters: any = {};
    
    if (employeeId) filters.employeeId = employeeId;
    if (status) filters.status = status;
    if (type) filters.type = type;
    if (isEmergency !== undefined) filters.isEmergency = isEmergency === 'true';
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (approvedBy) filters.approvedBy = approvedBy;
    if (requestedBy) filters.requestedBy = requestedBy;

    const result = await this.leavesService.findAll(
      parseInt(page),
      parseInt(limit),
      filters
    );

    return {
      success: true,
      message: 'Leave requests retrieved successfully',
      data: result.leaves,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: result.total,
        pages: Math.ceil(result.total / parseInt(limit))
      }
    };
  }

  @Get('statistics')
  @RequirePermissions(Permission.READ_LEAVES)
  async getStatistics() {
    return {
      success: true,
      message: 'Leave statistics retrieved successfully',
      data: await this.leavesService.getLeaveStatistics()
    };
  }

  @Get('employee/:employeeId')
  @RequirePermissions(Permission.READ_LEAVES)
  async findByEmployee(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    return {
      success: true,
      message: 'Employee leave requests retrieved successfully',
      data: await this.leavesService.findByEmployee(employeeId, start, end)
    };
  }

  @Get('employee/:employeeId/balance')
  @RequirePermissions(Permission.READ_LEAVES)
  async getLeaveBalance(
    @Param('employeeId') employeeId: string,
    @Query('year') year?: string,
  ) {
    const yearNum = year ? parseInt(year) : undefined;
    
    return {
      success: true,
      message: 'Leave balance retrieved successfully',
      data: await this.leavesService.getLeaveBalance(employeeId, yearNum)
    };
  }

  @Get(':id')
  @RequirePermissions(Permission.READ_LEAVES)
  async findOne(@Param('id') id: string) {
    return {
      success: true,
      message: 'Leave request retrieved successfully',
      data: await this.leavesService.findOne(id)
    };
  }

  @Patch(':id')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async update(@Param('id') id: string, @Body() updateLeaveDto: UpdateLeaveDto) {
    return {
      success: true,
      message: 'Leave request updated successfully',
      data: await this.leavesService.update(id, updateLeaveDto)
    };
  }

  @Post(':id/approve')
  @RequirePermissions(Permission.APPROVE_LEAVES)
  async approve(
    @Param('id') id: string,
    @Body() approveDto: ApproveLeaveDto,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Leave request approved successfully',
      data: await this.leavesService.approve(id, req.user._id, approveDto)
    };
  }

  @Post(':id/reject')
  @RequirePermissions(Permission.APPROVE_LEAVES)
  async reject(
    @Param('id') id: string,
    @Body() rejectDto: RejectLeaveDto,
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Leave request rejected successfully',
      data: await this.leavesService.reject(id, req.user._id, rejectDto)
    };
  }

  @Post(':id/cancel')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async cancel(
    @Param('id') id: string,
    @Body() body: { cancellationReason: string },
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Leave request cancelled successfully',
      data: await this.leavesService.cancel(id, req.user._id, body.cancellationReason)
    };
  }

  @Post(':id/return')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async markReturn(
    @Param('id') id: string,
    @Body() body: { returnDate: string }
  ) {
    return {
      success: true,
      message: 'Employee return marked successfully',
      data: await this.leavesService.markReturn(id, new Date(body.returnDate))
    };
  }

  @Post(':id/extend')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async requestExtension(
    @Param('id') id: string,
    @Body() body: { extensionDays: number; extensionReason: string }
  ) {
    return {
      success: true,
      message: 'Leave extension requested successfully',
      data: await this.leavesService.requestExtension(
        id,
        body.extensionDays,
        body.extensionReason
      )
    };
  }

  @Post(':id/comments')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  async addComment(
    @Param('id') id: string,
    @Body() body: { message: string },
    @Req() req: AuthenticatedRequest
  ) {
    return {
      success: true,
      message: 'Comment added successfully',
      data: await this.leavesService.addComment(id, req.user._id, body.message)
    };
  }

  @Delete(':id')
  @RequirePermissions(Permission.MANAGE_LEAVES)
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.leavesService.remove(id);
    return {
      success: true,
      message: 'Leave request deleted successfully'
    };
  }
}

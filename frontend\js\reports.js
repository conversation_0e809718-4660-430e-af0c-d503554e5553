// Reports management functionality

// Reports data
let reportsData = {
    availableReports: [],
    generatedReports: [],
    currentReport: null,
    filters: {
        fromDate: '',
        toDate: '',
        unit: '',
        reportType: ''
    }
};

// Load reports data
async function loadReportsData() {
    try {
        console.log('Loading reports data...');
        
        // Load available reports
        loadAvailableReports();
        
        // Load generated reports history
        await loadGeneratedReports();
        
        // Update UI
        updateAvailableReports();
        updateGeneratedReports();
        
        console.log('Reports data loaded successfully');
    } catch (error) {
        console.error('Error loading reports data:', error);
        loadAvailableReports();
        updateAvailableReports();
        updateGeneratedReports();
    }
}

// Load available reports
function loadAvailableReports() {
    reportsData.availableReports = [
        {
            id: 'employee_list',
            name: 'قائمة الموظفين',
            description: 'تقرير شامل بجميع الموظفين وبياناتهم',
            icon: 'fa-users',
            category: 'employees',
            parameters: ['unit', 'rank', 'status']
        },
        {
            id: 'attendance_summary',
            name: 'ملخص الحضور',
            description: 'تقرير ملخص الحضور والغياب للفترة المحددة',
            icon: 'fa-clock',
            category: 'attendance',
            parameters: ['fromDate', 'toDate', 'unit']
        },
        {
            id: 'attendance_detailed',
            name: 'تقرير الحضور التفصيلي',
            description: 'تقرير تفصيلي لحضور الموظفين يومياً',
            icon: 'fa-calendar-check',
            category: 'attendance',
            parameters: ['fromDate', 'toDate', 'employeeId']
        },
        {
            id: 'leaves_summary',
            name: 'ملخص الإجازات',
            description: 'تقرير ملخص الإجازات المطلوبة والموافق عليها',
            icon: 'fa-calendar-alt',
            category: 'leaves',
            parameters: ['fromDate', 'toDate', 'unit', 'leaveType']
        },
        {
            id: 'leaves_balance',
            name: 'رصيد الإجازات',
            description: 'تقرير رصيد الإجازات المتبقية لكل موظف',
            icon: 'fa-balance-scale',
            category: 'leaves',
            parameters: ['unit', 'employeeId']
        },
        {
            id: 'payroll_summary',
            name: 'ملخص الرواتب',
            description: 'تقرير ملخص الرواتب والبدلات للشهر المحدد',
            icon: 'fa-money-bill-wave',
            category: 'payroll',
            parameters: ['month', 'year', 'unit']
        },
        {
            id: 'performance_report',
            name: 'تقرير الأداء',
            description: 'تقرير تقييم أداء الموظفين للفترة المحددة',
            icon: 'fa-chart-line',
            category: 'performance',
            parameters: ['fromDate', 'toDate', 'unit']
        },
        {
            id: 'disciplinary_actions',
            name: 'الإجراءات التأديبية',
            description: 'تقرير الإجراءات التأديبية والعقوبات',
            icon: 'fa-gavel',
            category: 'disciplinary',
            parameters: ['fromDate', 'toDate', 'unit']
        }
    ];
}

// Load generated reports history
async function loadGeneratedReports() {
    try {
        const response = await apiService.getGeneratedReports();
        
        if (response && response.success) {
            reportsData.generatedReports = response.data.reports || [];
        } else {
            loadMockGeneratedReports();
        }
    } catch (error) {
        console.error('Error loading generated reports:', error);
        loadMockGeneratedReports();
    }
}

// Load mock generated reports
function loadMockGeneratedReports() {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    
    reportsData.generatedReports = [
        {
            id: 1,
            name: 'تقرير الحضور الشهري - يناير 2024',
            type: 'attendance_summary',
            generatedDate: today.toISOString(),
            generatedBy: 'المدير العام',
            fileSize: '2.5 MB',
            downloadUrl: '#',
            parameters: {
                fromDate: '2024-01-01',
                toDate: '2024-01-31',
                unit: 'جميع الوحدات'
            }
        },
        {
            id: 2,
            name: 'قائمة الموظفين النشطين',
            type: 'employee_list',
            generatedDate: yesterday.toISOString(),
            generatedBy: 'مدير الموارد البشرية',
            fileSize: '1.8 MB',
            downloadUrl: '#',
            parameters: {
                status: 'ACTIVE',
                unit: 'جميع الوحدات'
            }
        },
        {
            id: 3,
            name: 'تقرير رصيد الإجازات',
            type: 'leaves_balance',
            generatedDate: lastWeek.toISOString(),
            generatedBy: 'مدير الموارد البشرية',
            fileSize: '950 KB',
            downloadUrl: '#',
            parameters: {
                unit: 'الوحدة الأولى'
            }
        }
    ];
}

// Update available reports
function updateAvailableReports() {
    const reportsContainer = document.getElementById('availableReports');
    if (!reportsContainer) return;
    
    // Clear existing content
    reportsContainer.innerHTML = '';
    
    if (reportsData.availableReports.length === 0) {
        reportsContainer.innerHTML = '<p class="text-muted">لا توجد تقارير متاحة</p>';
        return;
    }
    
    // Group reports by category
    const groupedReports = reportsData.availableReports.reduce((groups, report) => {
        const category = report.category;
        if (!groups[category]) {
            groups[category] = [];
        }
        groups[category].push(report);
        return groups;
    }, {});
    
    // Create reports HTML
    const reportsHTML = Object.keys(groupedReports).map(category => {
        const categoryName = getCategoryName(category);
        const categoryReports = groupedReports[category];
        
        return `
            <div class="report-category">
                <h3>${categoryName}</h3>
                <div class="reports-grid">
                    ${categoryReports.map(report => `
                        <div class="report-card" onclick="selectReport('${report.id}')">
                            <div class="report-icon">
                                <i class="fas ${report.icon}"></i>
                            </div>
                            <div class="report-info">
                                <h4>${report.name}</h4>
                                <p>${report.description}</p>
                            </div>
                            <div class="report-actions">
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-play"></i>
                                    إنشاء
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }).join('');
    
    reportsContainer.innerHTML = reportsHTML;
}

// Get category name in Arabic
function getCategoryName(category) {
    const names = {
        employees: 'الموظفين',
        attendance: 'الحضور والانصراف',
        leaves: 'الإجازات',
        payroll: 'الرواتب',
        performance: 'الأداء',
        disciplinary: 'التأديب'
    };
    return names[category] || category;
}

// Update generated reports
function updateGeneratedReports() {
    const reportsContainer = document.getElementById('generatedReports');
    if (!reportsContainer) return;
    
    // Clear existing content
    reportsContainer.innerHTML = '';
    
    if (reportsData.generatedReports.length === 0) {
        reportsContainer.innerHTML = '<p class="text-muted">لا توجد تقارير منشأة</p>';
        return;
    }
    
    // Create reports table
    const tableHTML = `
        <table class="table">
            <thead>
                <tr>
                    <th>اسم التقرير</th>
                    <th>تاريخ الإنشاء</th>
                    <th>أنشأ بواسطة</th>
                    <th>حجم الملف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${reportsData.generatedReports.map(report => `
                    <tr>
                        <td><strong>${report.name}</strong></td>
                        <td>${formatDate(report.generatedDate)}</td>
                        <td>${report.generatedBy}</td>
                        <td>${report.fileSize}</td>
                        <td>
                            <div class="btn-group" style="display: flex; gap: 4px;">
                                <button class="btn btn-sm btn-info" onclick="viewReport(${report.id})" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="downloadReport(${report.id})" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteReport(${report.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    reportsContainer.innerHTML = tableHTML;
}

// Select report for generation
function selectReport(reportId) {
    const report = reportsData.availableReports.find(r => r.id === reportId);
    if (!report) {
        showError('لم يتم العثور على التقرير');
        return;
    }
    
    reportsData.currentReport = report;
    showReportParametersModal(report);
}

// Show report parameters modal
function showReportParametersModal(report) {
    console.log('Show report parameters modal for:', report.name);
    
    // For now, just show a simple prompt
    const confirmed = confirm(`هل تريد إنشاء تقرير "${report.name}"؟`);
    if (confirmed) {
        generateReport(report.id, {});
    }
}

// Generate report
async function generateReport(reportId, parameters) {
    try {
        showLoading(true);
        
        const response = await apiService.generateReport(reportId, parameters);
        
        if (response && response.success) {
            showSuccess('تم إنشاء التقرير بنجاح');
            
            // Refresh generated reports list
            await loadGeneratedReports();
            updateGeneratedReports();
            
            // Optionally download the report immediately
            if (response.data.downloadUrl) {
                window.open(response.data.downloadUrl, '_blank');
            }
        } else {
            showError('فشل في إنشاء التقرير');
        }
    } catch (error) {
        console.error('Error generating report:', error);
        showError('خطأ في إنشاء التقرير');
    } finally {
        showLoading(false);
    }
}

// View report
function viewReport(reportId) {
    const report = reportsData.generatedReports.find(r => r.id === reportId);
    if (!report) {
        showError('لم يتم العثور على التقرير');
        return;
    }
    
    console.log('View report:', report.name);
    showInfo(`عرض التقرير: ${report.name}`);
}

// Download report
function downloadReport(reportId) {
    const report = reportsData.generatedReports.find(r => r.id === reportId);
    if (!report) {
        showError('لم يتم العثور على التقرير');
        return;
    }
    
    console.log('Download report:', report.name);
    
    // For demo purposes, just show a message
    showInfo(`تحميل التقرير: ${report.name}`);
    
    // In real implementation, this would trigger file download
    // window.open(report.downloadUrl, '_blank');
}

// Delete report
async function deleteReport(reportId) {
    const report = reportsData.generatedReports.find(r => r.id === reportId);
    if (!report) {
        showError('لم يتم العثور على التقرير');
        return;
    }
    
    if (!confirm(`هل أنت متأكد من حذف التقرير "${report.name}"؟`)) {
        return;
    }
    
    try {
        showLoading(true);
        
        const response = await apiService.deleteReport(reportId);
        
        if (response && response.success) {
            showSuccess('تم حذف التقرير بنجاح');
            
            // Refresh generated reports list
            await loadGeneratedReports();
            updateGeneratedReports();
        } else {
            showError('فشل في حذف التقرير');
        }
    } catch (error) {
        console.error('Error deleting report:', error);
        showError('خطأ في حذف التقرير');
    } finally {
        showLoading(false);
    }
}

// Export report to different formats
async function exportReport(reportId, format) {
    try {
        showLoading(true);
        
        const response = await apiService.exportReport(reportId, format);
        
        if (response && response.success) {
            showSuccess(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`);
            
            if (response.data.downloadUrl) {
                window.open(response.data.downloadUrl, '_blank');
            }
        } else {
            showError('فشل في تصدير التقرير');
        }
    } catch (error) {
        console.error('Error exporting report:', error);
        showError('خطأ في تصدير التقرير');
    } finally {
        showLoading(false);
    }
}

// Schedule report generation
function scheduleReport(reportId, schedule) {
    console.log('Schedule report:', reportId, schedule);
    showInfo('سيتم إضافة ميزة جدولة التقارير قريباً');
}

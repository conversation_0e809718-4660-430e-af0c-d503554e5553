// Main JavaScript file for Employee Management System

// Global variables
let currentUser = null;
let currentPage = 'dashboard';

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize the application
async function initializeApp() {
    try {
        // Check authentication
        if (!checkAuthentication()) {
            redirectToLogin();
            return;
        }

        // Load user data
        await loadUserData();
        
        // Initialize navigation
        initializeNavigation();
        
        // Load initial page
        showPage('dashboard');
        
        // Initialize event listeners
        initializeEventListeners();
        
        console.log('Application initialized successfully');
    } catch (error) {
        console.error('Application initialization error:', error);
        showError('خطأ في تهيئة التطبيق');
    }
}

// Check if user is authenticated
function checkAuthentication() {
    const token = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    return !!token;
}

// Redirect to login page
function redirectToLogin() {
    window.location.href = 'login.html';
}

// Load user data from localStorage
async function loadUserData() {
    try {
        const userData = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
        if (userData) {
            currentUser = JSON.parse(userData);
            updateUserInterface();
        }
    } catch (error) {
        console.error('Error loading user data:', error);
    }
}

// Update user interface with user data
function updateUserInterface() {
    if (currentUser) {
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = currentUser.fullName || currentUser.username;
        }
    }
}

// Initialize navigation
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            if (page) {
                showPage(page);
            }
        });
    });
}

// Show specific page
function showPage(pageName) {
    // Hide all pages
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });

    // Show selected page
    const targetPage = document.getElementById(`${pageName}-page`);
    if (targetPage) {
        targetPage.classList.add('active');
        currentPage = pageName;
        
        // Update navigation
        updateNavigation(pageName);
        
        // Load page data
        loadPageData(pageName);
    }
}

// Update navigation active state
function updateNavigation(activePage) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-page') === activePage) {
            link.classList.add('active');
        }
    });
}

// Load data for specific page
async function loadPageData(pageName) {
    try {
        showLoading(true);

        switch (pageName) {
            case 'dashboard':
                if (typeof loadDashboardData === 'function') {
                    await loadDashboardData();
                }
                break;
            case 'employees':
                if (typeof loadEmployeesData === 'function') {
                    await loadEmployeesData();
                }
                break;
            case 'attendance':
                if (typeof loadAttendanceData === 'function') {
                    await loadAttendanceData();
                }
                break;
            case 'leaves':
                if (typeof loadLeavesData === 'function') {
                    await loadLeavesData();
                }
                break;
            case 'reports':
                if (typeof loadReportsData === 'function') {
                    await loadReportsData();
                }
                break;
        }
    } catch (error) {
        console.error(`Error loading ${pageName} data:`, error);
        showError(`خطأ في تحميل بيانات ${getPageTitle(pageName)}`);
    } finally {
        showLoading(false);
    }
}

// Get page title in Arabic
function getPageTitle(pageName) {
    const titles = {
        dashboard: 'لوحة التحكم',
        employees: 'الموظفين',
        attendance: 'الحضور',
        leaves: 'الإجازات',
        reports: 'التقارير'
    };
    return titles[pageName] || pageName;
}

// Initialize event listeners
function initializeEventListeners() {
    // Logout button
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }

    // Search functionality
    const searchInputs = document.querySelectorAll('input[type="search"], .search-box input');
    searchInputs.forEach(input => {
        input.addEventListener('input', UTILS.debounce(handleSearch, APP_CONFIG.DEBOUNCE_DELAY));
    });

    // Filter functionality
    const filterSelects = document.querySelectorAll('.filter-group select');
    filterSelects.forEach(select => {
        select.addEventListener('change', handleFilter);
    });
}

// Handle search functionality
function handleSearch(event) {
    const searchTerm = event.target.value.trim();
    const pageType = getCurrentPageType();
    
    console.log(`Searching ${pageType} for: ${searchTerm}`);
    
    // Implement search based on current page
    switch (currentPage) {
        case 'employees':
            if (typeof searchEmployees === 'function') {
                searchEmployees(searchTerm);
            }
            break;
        case 'attendance':
            if (typeof searchAttendance === 'function') {
                searchAttendance(searchTerm);
            }
            break;
        case 'leaves':
            if (typeof searchLeaves === 'function') {
                searchLeaves(searchTerm);
            }
            break;
    }
}

// Handle filter functionality
function handleFilter(event) {
    const filterType = event.target.id;
    const filterValue = event.target.value;
    
    console.log(`Filtering by ${filterType}: ${filterValue}`);
    
    // Implement filtering based on current page
    switch (currentPage) {
        case 'employees':
            if (typeof filterEmployees === 'function') {
                filterEmployees(filterType, filterValue);
            }
            break;
        case 'attendance':
            if (typeof filterAttendance === 'function') {
                filterAttendance(filterType, filterValue);
            }
            break;
        case 'leaves':
            if (typeof filterLeaves === 'function') {
                filterLeaves(filterType, filterValue);
            }
            break;
    }
}

// Get current page type
function getCurrentPageType() {
    return currentPage;
}

// Logout function
async function logout() {
    try {
        showLoading(true);
        
        // Call logout API
        await apiService.logout();
        
        // Clear local storage
        localStorage.clear();
        
        // Redirect to login
        window.location.href = 'login.html';
        
    } catch (error) {
        console.error('Logout error:', error);
        // Force logout even if API call fails
        localStorage.clear();
        window.location.href = 'login.html';
    }
}

// Show/hide loading overlay
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// Show error message
function showError(message) {
    showToast(message, 'error');
}

// Show success message
function showSuccess(message) {
    showToast(message, 'success');
}

// Show info message
function showInfo(message) {
    showToast(message, 'info');
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast to container
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // Auto remove after duration
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, APP_CONFIG.TOAST_DURATION);
}

// Get toast icon based on type
function getToastIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-triangle',
        warning: 'fa-exclamation-circle',
        info: 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

// Format date for display
function formatDate(date) {
    return UTILS.formatDate(date);
}

// Format currency for display
function formatCurrency(amount) {
    return UTILS.formatCurrency(amount);
}

// Validate form data
function validateForm(formData, rules) {
    const errors = [];
    
    for (const field in rules) {
        const value = formData[field];
        const rule = rules[field];
        
        if (rule.required && (!value || value.trim() === '')) {
            errors.push(`${rule.label} مطلوب`);
            continue;
        }
        
        if (value && rule.minLength && value.length < rule.minLength) {
            errors.push(`${rule.label} يجب أن يكون على الأقل ${rule.minLength} أحرف`);
        }
        
        if (value && rule.maxLength && value.length > rule.maxLength) {
            errors.push(`${rule.label} يجب أن يكون أقل من ${rule.maxLength} حرف`);
        }
        
        if (value && rule.pattern && !rule.pattern.test(value)) {
            errors.push(`${rule.label} غير صحيح`);
        }
        
        if (value && rule.type === 'email' && !UTILS.isValidEmail(value)) {
            errors.push(`${rule.label} غير صحيح`);
        }
        
        if (value && rule.type === 'phone' && !UTILS.isValidPhone(value)) {
            errors.push(`${rule.label} غير صحيح`);
        }
    }
    
    return errors;
}

// Show form validation errors
function showFormErrors(errors) {
    if (errors.length > 0) {
        const errorMessage = errors.join('\n');
        showError(errorMessage);
        return false;
    }
    return true;
}

// Utility function to get form data
function getFormData(formElement) {
    const formData = new FormData(formElement);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    return data;
}

// Utility function to populate form
function populateForm(formElement, data) {
    for (const key in data) {
        const input = formElement.querySelector(`[name="${key}"]`);
        if (input) {
            if (input.type === 'checkbox') {
                input.checked = data[key];
            } else {
                input.value = data[key] || '';
            }
        }
    }
}

// Add CSS for toast notifications
const toastCSS = `
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.toast-success {
    background: #10b981;
    color: white;
}

.toast-error {
    background: #ef4444;
    color: white;
}

.toast-warning {
    background: #f59e0b;
    color: white;
}

.toast-info {
    background: #06b6d4;
    color: white;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    opacity: 0.8;
}

.toast-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
`;

// Add toast CSS to document
const style = document.createElement('style');
style.textContent = toastCSS;
document.head.appendChild(style);

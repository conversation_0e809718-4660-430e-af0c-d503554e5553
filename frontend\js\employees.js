// Employees management functionality

// Employees data
let employeesData = {
    employees: [],
    currentPage: 1,
    totalPages: 1,
    pageSize: 10,
    totalCount: 0,
    filters: {
        search: '',
        unit: '',
        rank: '',
        status: ''
    }
};

// Load employees data
async function loadEmployeesData() {
    try {
        console.log('Loading employees data...');
        
        const params = {
            page: employeesData.currentPage,
            limit: employeesData.pageSize,
            search: employeesData.filters.search,
            unit: employeesData.filters.unit,
            rank: employeesData.filters.rank,
            status: employeesData.filters.status
        };
        
        // Remove empty parameters
        Object.keys(params).forEach(key => {
            if (!params[key]) delete params[key];
        });
        
        const response = await apiService.getEmployees(params);
        
        if (response && response.success) {
            employeesData.employees = response.data.employees || [];
            employeesData.totalCount = response.data.totalCount || 0;
            employeesData.totalPages = Math.ceil(employeesData.totalCount / employeesData.pageSize);
        } else {
            // Use mock data if API fails
            loadMockEmployeesData();
        }
        
        // Update UI
        updateEmployeesTable();
        updateEmployeesPagination();
        updateEmployeesFilters();
        
        console.log('Employees data loaded successfully');
    } catch (error) {
        console.error('Error loading employees data:', error);
        loadMockEmployeesData();
        updateEmployeesTable();
        updateEmployeesPagination();
        updateEmployeesFilters();
    }
}

// Load mock employees data
function loadMockEmployeesData() {
    employeesData.employees = [
        {
            id: 1,
            militaryNumber: 'M001',
            fullName: 'أحمد محمد علي',
            rank: 'نقيب',
            unit: 'الوحدة الأولى',
            position: 'قائد فصيل',
            status: 'ACTIVE',
            photo: null,
            phoneNumber: '0501234567',
            email: '<EMAIL>',
            appointmentDate: '2020-01-15',
            bloodType: 'A+',
            motherName: 'فاطمة أحمد'
        },
        {
            id: 2,
            militaryNumber: 'M002',
            fullName: 'سارة أحمد خالد',
            rank: 'ملازم أول',
            unit: 'وحدة الإشارة',
            position: 'ضابط إشارة',
            status: 'ACTIVE',
            photo: null,
            phoneNumber: '0507654321',
            email: '<EMAIL>',
            appointmentDate: '2021-03-10',
            bloodType: 'B+',
            motherName: 'عائشة محمد'
        },
        {
            id: 3,
            militaryNumber: 'M003',
            fullName: 'محمد علي حسن',
            rank: 'رقيب أول',
            unit: 'الوحدة الثانية',
            position: 'رقيب أول',
            status: 'ACTIVE',
            photo: null,
            phoneNumber: '0509876543',
            email: '<EMAIL>',
            appointmentDate: '2019-06-20',
            bloodType: 'O+',
            motherName: 'خديجة علي'
        },
        {
            id: 4,
            militaryNumber: 'M004',
            fullName: 'فاطمة خالد محمد',
            rank: 'ملازم',
            unit: 'وحدة الطب العسكري',
            position: 'طبيب عسكري',
            status: 'ACTIVE',
            photo: null,
            phoneNumber: '0502468135',
            email: '<EMAIL>',
            appointmentDate: '2022-01-05',
            bloodType: 'AB+',
            motherName: 'زينب أحمد'
        },
        {
            id: 5,
            militaryNumber: 'M005',
            fullName: 'عبدالله سعد الغامدي',
            rank: 'عقيد',
            unit: 'الوحدة الأولى',
            position: 'قائد وحدة',
            status: 'ACTIVE',
            photo: null,
            phoneNumber: '0501357924',
            email: '<EMAIL>',
            appointmentDate: '2015-09-12',
            bloodType: 'A-',
            motherName: 'نورا سعد'
        }
    ];
    
    employeesData.totalCount = employeesData.employees.length;
    employeesData.totalPages = Math.ceil(employeesData.totalCount / employeesData.pageSize);
}

// Update employees table
function updateEmployeesTable() {
    const tableBody = document.getElementById('employeesTableBody');
    if (!tableBody) return;
    
    // Clear existing content
    tableBody.innerHTML = '';
    
    if (employeesData.employees.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted" style="padding: 40px;">
                    <i class="fas fa-users" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <p>لا توجد موظفين للعرض</p>
                </td>
            </tr>
        `;
        return;
    }
    
    // Create table rows
    const tableHTML = employeesData.employees.map(employee => `
        <tr>
            <td>
                <div class="employee-photo">
                    ${employee.photo ? 
                        `<img src="${employee.photo}" alt="${employee.fullName}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">` :
                        `<div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gray-200); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user" style="color: var(--gray-500);"></i>
                        </div>`
                    }
                </div>
            </td>
            <td>
                <div>
                    <strong>${employee.fullName}</strong>
                    <br>
                    <small class="text-muted">${employee.email || ''}</small>
                </div>
            </td>
            <td><strong>${employee.militaryNumber}</strong></td>
            <td>${employee.rank}</td>
            <td>${employee.unit}</td>
            <td>${employee.position}</td>
            <td>
                <span class="badge ${getStatusBadgeClass(employee.status)}">
                    ${getStatusText(employee.status)}
                </span>
            </td>
            <td>
                <div class="btn-group" style="display: flex; gap: 4px;">
                    <button class="btn btn-sm btn-info" onclick="viewEmployee(${employee.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editEmployee(${employee.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteEmployee(${employee.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = tableHTML;
}

// Get status badge class
function getStatusBadgeClass(status) {
    const classes = {
        'ACTIVE': 'badge-success',
        'INACTIVE': 'badge-secondary',
        'SUSPENDED': 'badge-warning',
        'TERMINATED': 'badge-danger'
    };
    return classes[status] || 'badge-secondary';
}

// Get status text in Arabic
function getStatusText(status) {
    const texts = {
        'ACTIVE': 'نشط',
        'INACTIVE': 'غير نشط',
        'SUSPENDED': 'موقوف',
        'TERMINATED': 'منهي الخدمة'
    };
    return texts[status] || status;
}

// Update employees pagination
function updateEmployeesPagination() {
    const paginationContainer = document.getElementById('employeesPagination');
    if (!paginationContainer) return;
    
    // Clear existing content
    paginationContainer.innerHTML = '';
    
    if (employeesData.totalPages <= 1) return;
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <button ${employeesData.currentPage === 1 ? 'disabled' : ''} 
                onclick="changeEmployeesPage(${employeesData.currentPage - 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // Page numbers
    const startPage = Math.max(1, employeesData.currentPage - 2);
    const endPage = Math.min(employeesData.totalPages, employeesData.currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button ${i === employeesData.currentPage ? 'class="active"' : ''} 
                    onclick="changeEmployeesPage(${i})">
                ${i}
            </button>
        `;
    }
    
    // Next button
    paginationHTML += `
        <button ${employeesData.currentPage === employeesData.totalPages ? 'disabled' : ''} 
                onclick="changeEmployeesPage(${employeesData.currentPage + 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// Update employees filters
function updateEmployeesFilters() {
    // Update unit filter
    const unitFilter = document.getElementById('unitFilter');
    if (unitFilter && unitFilter.children.length <= 1) {
        MILITARY_UNITS.forEach(unit => {
            const option = document.createElement('option');
            option.value = unit;
            option.textContent = unit;
            unitFilter.appendChild(option);
        });
    }
    
    // Update rank filter
    const rankFilter = document.getElementById('rankFilter');
    if (rankFilter && rankFilter.children.length <= 1) {
        MILITARY_RANKS.forEach(rank => {
            const option = document.createElement('option');
            option.value = rank;
            option.textContent = rank;
            rankFilter.appendChild(option);
        });
    }
}

// Change employees page
function changeEmployeesPage(page) {
    if (page < 1 || page > employeesData.totalPages) return;
    
    employeesData.currentPage = page;
    loadEmployeesData();
}

// Search employees
function searchEmployees(searchTerm) {
    employeesData.filters.search = searchTerm;
    employeesData.currentPage = 1;
    loadEmployeesData();
}

// Filter employees
function filterEmployees(filterType, filterValue) {
    switch (filterType) {
        case 'unitFilter':
            employeesData.filters.unit = filterValue;
            break;
        case 'rankFilter':
            employeesData.filters.rank = filterValue;
            break;
        case 'statusFilter':
            employeesData.filters.status = filterValue;
            break;
    }
    
    employeesData.currentPage = 1;
    loadEmployeesData();
}

// View employee details
function viewEmployee(employeeId) {
    const employee = employeesData.employees.find(emp => emp.id === employeeId);
    if (!employee) {
        showError('لم يتم العثور على الموظف');
        return;
    }
    
    // Show employee details modal
    showEmployeeDetailsModal(employee);
}

// Edit employee
function editEmployee(employeeId) {
    const employee = employeesData.employees.find(emp => emp.id === employeeId);
    if (!employee) {
        showError('لم يتم العثور على الموظف');
        return;
    }
    
    // Show edit employee modal
    showEditEmployeeModal(employee);
}

// Delete employee
async function deleteEmployee(employeeId) {
    const employee = employeesData.employees.find(emp => emp.id === employeeId);
    if (!employee) {
        showError('لم يتم العثور على الموظف');
        return;
    }
    
    // Confirm deletion
    if (!confirm(`هل أنت متأكد من حذف الموظف ${employee.fullName}؟`)) {
        return;
    }
    
    try {
        showLoading(true);
        
        // Call delete API
        const response = await apiService.deleteEmployee(employeeId);
        
        if (response && response.success) {
            showSuccess('تم حذف الموظف بنجاح');
            loadEmployeesData();
        } else {
            showError('فشل في حذف الموظف');
        }
    } catch (error) {
        console.error('Error deleting employee:', error);
        showError('خطأ في حذف الموظف');
    } finally {
        showLoading(false);
    }
}

// Show add employee modal
function showAddEmployeeModal() {
    // This will be implemented when we create the modal
    console.log('Show add employee modal');
    showInfo('سيتم إضافة نافذة إضافة موظف قريباً');
}

// Show employee details modal
function showEmployeeDetailsModal(employee) {
    // This will be implemented when we create the modal
    console.log('Show employee details modal for:', employee.fullName);
    showInfo(`عرض تفاصيل الموظف: ${employee.fullName}`);
}

// Show edit employee modal
function showEditEmployeeModal(employee) {
    // This will be implemented when we create the modal
    console.log('Show edit employee modal for:', employee.fullName);
    showInfo(`تعديل بيانات الموظف: ${employee.fullName}`);
}

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { UserRole, Permission } from '@shared/types/user.types';

export type UserDocument = User & Document;

@Schema({
  timestamps: true,
  collection: 'users'
})
export class User {
  @Prop({ required: true, unique: true, trim: true })
  username: string;

  @Prop({ required: true, unique: true, lowercase: true, trim: true })
  email: string;

  @Prop({ required: true, select: false })
  password: string;

  @Prop({ 
    required: true, 
    enum: Object.values(UserRole),
    default: UserRole.EMPLOYEE 
  })
  role: UserRole;

  @Prop({ 
    type: [String], 
    enum: Object.values(Permission),
    default: [] 
  })
  permissions: Permission[];

  @Prop({
    type: {
      fullName: { type: String, required: true },
      phone: { type: String },
      department: { type: String },
      position: { type: String },
      avatar: { type: String }
    },
    required: true
  })
  profile: {
    fullName: string;
    phone?: string;
    department?: string;
    position?: string;
    avatar?: string;
  };

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  lastLogin: Date;

  @Prop({ default: 0 })
  loginAttempts: number;

  @Prop()
  lockUntil: Date;

  @Prop()
  passwordResetToken: string;

  @Prop()
  passwordResetExpires: Date;

  @Prop()
  refreshToken: string;

  @Prop()
  refreshTokenExpires: Date;

  // Virtual for checking if account is locked
  get isLocked(): boolean {
    return !!(this.lockUntil && this.lockUntil > new Date());
  }

  // Virtual for checking if password reset token is valid
  get isPasswordResetTokenValid(): boolean {
    return !!(this.passwordResetToken && 
              this.passwordResetExpires && 
              this.passwordResetExpires > new Date());
  }
}

export const UserSchema = SchemaFactory.createForClass(User);

// Indexes
UserSchema.index({ username: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ refreshToken: 1 });
UserSchema.index({ passwordResetToken: 1 });

// Virtual fields
UserSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > new Date());
});

UserSchema.virtual('isPasswordResetTokenValid').get(function() {
  return !!(this.passwordResetToken && 
            this.passwordResetExpires && 
            this.passwordResetExpires > new Date());
});

// Pre-save middleware to handle password hashing
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  const bcrypt = require('bcrypt');
  const saltRounds = 12;
  this.password = await bcrypt.hash(this.password, saltRounds);
  next();
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  const bcrypt = require('bcrypt');
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
UserSchema.methods.incLoginAttempts = function(): Promise<UserDocument> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates: any = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000) }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
UserSchema.methods.resetLoginAttempts = function(): Promise<UserDocument> {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

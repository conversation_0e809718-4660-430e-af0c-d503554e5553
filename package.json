{"name": "employee-management-system", "version": "1.0.0", "description": "نظام إدارة شؤون الموظفين - Employee Management System", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:services\" \"npm run dev:frontend\"", "dev:services": "concurrently \"npm run dev:auth\" \"npm run dev:employee\" \"npm run dev:report\" \"npm run dev:barcode\" \"npm run dev:file\" \"npm run dev:gateway\"", "dev:auth": "cd services/auth-service && npm run start:dev", "dev:employee": "cd services/employee-service && npm run start:dev", "dev:report": "cd services/report-service && npm run start:dev", "dev:barcode": "cd services/barcode-service && npm run start:dev", "dev:file": "cd services/file-service && npm run start:dev", "dev:gateway": "cd services/api-gateway && npm run start:dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:services && npm run build:frontend", "build:services": "concurrently \"cd services/auth-service && npm run build\" \"cd services/employee-service && npm run build\" \"cd services/report-service && npm run build\" \"cd services/barcode-service && npm run build\" \"cd services/file-service && npm run build\" \"cd services/api-gateway && npm run build\"", "build:frontend": "cd frontend && npm run build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "install:all": "npm install && npm run install:services && npm run install:frontend", "install:services": "concurrently \"cd services/auth-service && npm install\" \"cd services/employee-service && npm install\" \"cd services/report-service && npm install\" \"cd services/barcode-service && npm install\" \"cd services/file-service && npm install\" \"cd services/api-gateway && npm install\"", "install:frontend": "cd frontend && npm install"}, "keywords": ["employee-management", "microservices", "<PERSON><PERSON><PERSON>", "react", "mongodb", "arabic"], "author": "Employee Management Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}
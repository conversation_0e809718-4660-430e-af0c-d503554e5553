export interface Report {
  _id?: string;
  title: string;
  type: ReportType;
  description?: string;
  filters: ReportFilters;
  data: any[];
  metadata: ReportMetadata;
  generatedBy: string;
  generatedAt: Date;
  format: ReportFormat;
  filePath?: string;
  isScheduled: boolean;
  scheduleConfig?: ScheduleConfig;
}

export interface ReportFilters {
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  units?: string[];
  ranks?: string[];
  departments?: string[];
  employeeIds?: string[];
  status?: string[];
  customFilters?: Record<string, any>;
}

export interface ReportMetadata {
  totalRecords: number;
  generationTime: number; // in milliseconds
  fileSize?: number; // in bytes
  columns: ReportColumn[];
  summary?: Record<string, any>;
}

export interface ReportColumn {
  key: string;
  label: string;
  labelAr: string;
  type: ColumnType;
  width?: number;
  format?: string;
  sortable?: boolean;
  filterable?: boolean;
}

export interface ScheduleConfig {
  frequency: ScheduleFrequency;
  time: string; // HH:mm format
  dayOfWeek?: number; // 0-6 (Sunday-Saturday)
  dayOfMonth?: number; // 1-31
  recipients: string[]; // email addresses
  isActive: boolean;
}

export interface CreateReportDto {
  title: string;
  type: ReportType;
  description?: string;
  filters: ReportFilters;
  format: ReportFormat;
  isScheduled?: boolean;
  scheduleConfig?: ScheduleConfig;
}

export interface ReportTemplate {
  _id?: string;
  name: string;
  nameAr: string;
  type: ReportType;
  description: string;
  descriptionAr: string;
  defaultFilters: ReportFilters;
  columns: ReportColumn[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Enums
export enum ReportType {
  EMPLOYEE_LIST = 'EMPLOYEE_LIST',
  ATTENDANCE_SUMMARY = 'ATTENDANCE_SUMMARY',
  LEAVE_REPORT = 'LEAVE_REPORT',
  PUNISHMENT_REPORT = 'PUNISHMENT_REPORT',
  REWARD_REPORT = 'REWARD_REPORT',
  SALARY_REPORT = 'SALARY_REPORT',
  UNIT_REPORT = 'UNIT_REPORT',
  RANK_DISTRIBUTION = 'RANK_DISTRIBUTION',
  EMPLOYEE_PROFILE = 'EMPLOYEE_PROFILE',
  ID_CARD = 'ID_CARD',
  CERTIFICATE = 'CERTIFICATE',
  CUSTOM = 'CUSTOM'
}

export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  WORD = 'WORD',
  CSV = 'CSV',
  JSON = 'JSON'
}

export enum ColumnType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  IMAGE = 'IMAGE',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE'
}

export enum ScheduleFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  YEARLY = 'YEARLY'
}

// Report Templates Configuration
export const REPORT_TEMPLATES: Record<ReportType, Partial<ReportTemplate>> = {
  [ReportType.EMPLOYEE_LIST]: {
    name: 'Employee List',
    nameAr: 'قائمة الموظفين',
    description: 'Complete list of employees with their basic information',
    descriptionAr: 'قائمة شاملة بالموظفين مع معلوماتهم الأساسية',
    columns: [
      { key: 'militaryNumber', label: 'Military Number', labelAr: 'الرقم العسكري', type: ColumnType.TEXT },
      { key: 'fullName', label: 'Full Name', labelAr: 'الاسم الكامل', type: ColumnType.TEXT },
      { key: 'rank', label: 'Rank', labelAr: 'الرتبة', type: ColumnType.TEXT },
      { key: 'unit', label: 'Unit', labelAr: 'الوحدة', type: ColumnType.TEXT },
      { key: 'appointmentDate', label: 'Appointment Date', labelAr: 'تاريخ التعيين', type: ColumnType.DATE },
      { key: 'status', label: 'Status', labelAr: 'الحالة', type: ColumnType.TEXT }
    ]
  },
  [ReportType.ATTENDANCE_SUMMARY]: {
    name: 'Attendance Summary',
    nameAr: 'ملخص الحضور',
    description: 'Employee attendance summary for specified period',
    descriptionAr: 'ملخص حضور الموظفين للفترة المحددة',
    columns: [
      { key: 'employeeName', label: 'Employee Name', labelAr: 'اسم الموظف', type: ColumnType.TEXT },
      { key: 'totalDays', label: 'Total Days', labelAr: 'إجمالي الأيام', type: ColumnType.NUMBER },
      { key: 'presentDays', label: 'Present Days', labelAr: 'أيام الحضور', type: ColumnType.NUMBER },
      { key: 'absentDays', label: 'Absent Days', labelAr: 'أيام الغياب', type: ColumnType.NUMBER },
      { key: 'lateDays', label: 'Late Days', labelAr: 'أيام التأخير', type: ColumnType.NUMBER },
      { key: 'attendanceRate', label: 'Attendance Rate', labelAr: 'معدل الحضور', type: ColumnType.PERCENTAGE }
    ]
  },
  [ReportType.LEAVE_REPORT]: {
    name: 'Leave Report',
    nameAr: 'تقرير الإجازات',
    description: 'Employee leave requests and balances',
    descriptionAr: 'طلبات إجازات الموظفين والأرصدة',
    columns: [
      { key: 'employeeName', label: 'Employee Name', labelAr: 'اسم الموظف', type: ColumnType.TEXT },
      { key: 'leaveType', label: 'Leave Type', labelAr: 'نوع الإجازة', type: ColumnType.TEXT },
      { key: 'startDate', label: 'Start Date', labelAr: 'تاريخ البداية', type: ColumnType.DATE },
      { key: 'endDate', label: 'End Date', labelAr: 'تاريخ النهاية', type: ColumnType.DATE },
      { key: 'duration', label: 'Duration (Days)', labelAr: 'المدة (أيام)', type: ColumnType.NUMBER },
      { key: 'status', label: 'Status', labelAr: 'الحالة', type: ColumnType.TEXT }
    ]
  },
  [ReportType.EMPLOYEE_PROFILE]: {
    name: 'Employee Profile',
    nameAr: 'ملف الموظف',
    description: 'Detailed employee profile information',
    descriptionAr: 'معلومات مفصلة عن ملف الموظف',
    columns: [
      { key: 'photo', label: 'Photo', labelAr: 'الصورة', type: ColumnType.IMAGE },
      { key: 'personalInfo', label: 'Personal Information', labelAr: 'المعلومات الشخصية', type: ColumnType.TEXT },
      { key: 'jobInfo', label: 'Job Information', labelAr: 'المعلومات الوظيفية', type: ColumnType.TEXT },
      { key: 'administrativeInfo', label: 'Administrative Information', labelAr: 'المعلومات الإدارية', type: ColumnType.TEXT }
    ]
  },
  [ReportType.ID_CARD]: {
    name: 'ID Card',
    nameAr: 'بطاقة الهوية',
    description: 'Employee identification card with barcode',
    descriptionAr: 'بطاقة هوية الموظف مع الباركود',
    columns: [
      { key: 'photo', label: 'Photo', labelAr: 'الصورة', type: ColumnType.IMAGE },
      { key: 'fullName', label: 'Full Name', labelAr: 'الاسم الكامل', type: ColumnType.TEXT },
      { key: 'militaryNumber', label: 'Military Number', labelAr: 'الرقم العسكري', type: ColumnType.TEXT },
      { key: 'rank', label: 'Rank', labelAr: 'الرتبة', type: ColumnType.TEXT },
      { key: 'unit', label: 'Unit', labelAr: 'الوحدة', type: ColumnType.TEXT },
      { key: 'barcode', label: 'Barcode', labelAr: 'الباركود', type: ColumnType.IMAGE }
    ]
  }
};

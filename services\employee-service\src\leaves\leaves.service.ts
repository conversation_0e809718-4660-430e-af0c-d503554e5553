import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Leave, LeaveDocument } from './schemas/leave.schema';
import { LeaveType, LeaveStatus } from '@shared/types/employee.types';

export interface CreateLeaveDto {
  employeeId: string;
  type: LeaveType;
  startDate: Date;
  endDate: Date;
  reason: string;
  description?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  isEmergency?: boolean;
  replacementEmployee?: string;
  handoverNotes?: string;
}

export interface UpdateLeaveDto {
  type?: LeaveType;
  startDate?: Date;
  endDate?: Date;
  reason?: string;
  description?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  replacementEmployee?: string;
  handoverNotes?: string;
}

export interface ApproveLeaveDto {
  comments?: string;
}

export interface RejectLeaveDto {
  rejectionReason: string;
  comments?: string;
}

@Injectable()
export class LeavesService {
  constructor(
    @InjectModel(Leave.name) private leaveModel: Model<LeaveDocument>,
  ) {}

  async create(createLeaveDto: CreateLeaveDto, requestedBy: string): Promise<Leave> {
    // Validate dates
    const startDate = new Date(createLeaveDto.startDate);
    const endDate = new Date(createLeaveDto.endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (startDate < today && !createLeaveDto.isEmergency) {
      throw new BadRequestException('Leave start date cannot be in the past');
    }

    if (endDate < startDate) {
      throw new BadRequestException('Leave end date cannot be before start date');
    }

    // Check for overlapping leaves
    const overlappingLeave = await this.leaveModel.findOne({
      employeeId: new Types.ObjectId(createLeaveDto.employeeId),
      status: { $in: [LeaveStatus.PENDING, LeaveStatus.APPROVED] },
      $or: [
        {
          startDate: { $lte: endDate },
          endDate: { $gte: startDate }
        }
      ]
    });

    if (overlappingLeave) {
      throw new ConflictException('Employee already has a leave request for overlapping dates');
    }

    const leave = new this.leaveModel({
      ...createLeaveDto,
      employeeId: new Types.ObjectId(createLeaveDto.employeeId),
      requestedBy,
      status: createLeaveDto.isEmergency ? LeaveStatus.APPROVED : LeaveStatus.PENDING
    });

    return leave.save();
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    filters?: any
  ): Promise<{ leaves: Leave[], total: number }> {
    const skip = (page - 1) * limit;
    const query = this.buildQuery(filters);

    const [leaves, total] = await Promise.all([
      this.leaveModel
        .find(query)
        .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.leaveModel.countDocuments(query)
    ]);

    return { leaves, total };
  }

  async findOne(id: string): Promise<Leave> {
    const leave = await this.leaveModel
      .findById(id)
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    if (!leave) {
      throw new NotFoundException('Leave request not found');
    }

    return leave;
  }

  async findByEmployee(
    employeeId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<Leave[]> {
    const query: any = { employeeId: new Types.ObjectId(employeeId) };

    if (startDate || endDate) {
      query.startDate = {};
      if (startDate) query.startDate.$gte = startDate;
      if (endDate) query.startDate.$lte = endDate;
    }

    return this.leaveModel
      .find(query)
      .sort({ startDate: -1 })
      .exec();
  }

  async update(id: string, updateLeaveDto: UpdateLeaveDto): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.PENDING) {
      throw new BadRequestException('Only pending leave requests can be updated');
    }

    // Validate dates if being updated
    if (updateLeaveDto.startDate || updateLeaveDto.endDate) {
      const startDate = updateLeaveDto.startDate || leave.startDate;
      const endDate = updateLeaveDto.endDate || leave.endDate;

      if (endDate < startDate) {
        throw new BadRequestException('Leave end date cannot be before start date');
      }

      // Check for overlapping leaves (excluding current leave)
      const overlappingLeave = await this.leaveModel.findOne({
        _id: { $ne: id },
        employeeId: leave.employeeId,
        status: { $in: [LeaveStatus.PENDING, LeaveStatus.APPROVED] },
        $or: [
          {
            startDate: { $lte: endDate },
            endDate: { $gte: startDate }
          }
        ]
      });

      if (overlappingLeave) {
        throw new ConflictException('Employee already has a leave request for overlapping dates');
      }
    }

    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(id, updateLeaveDto, { new: true })
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    return updatedLeave;
  }

  async approve(id: string, approvedBy: string, approveDto?: ApproveLeaveDto): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.PENDING) {
      throw new BadRequestException('Only pending leave requests can be approved');
    }

    const updateData: any = {
      status: LeaveStatus.APPROVED,
      approvedBy,
      approvedAt: new Date()
    };

    if (approveDto?.comments) {
      updateData.$push = {
        comments: {
          author: approvedBy,
          message: approveDto.comments,
          timestamp: new Date()
        }
      };
    }

    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    return updatedLeave;
  }

  async reject(id: string, rejectedBy: string, rejectDto: RejectLeaveDto): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.PENDING) {
      throw new BadRequestException('Only pending leave requests can be rejected');
    }

    const updateData: any = {
      status: LeaveStatus.REJECTED,
      rejectedBy,
      rejectedAt: new Date(),
      rejectionReason: rejectDto.rejectionReason
    };

    if (rejectDto.comments) {
      updateData.$push = {
        comments: {
          author: rejectedBy,
          message: rejectDto.comments,
          timestamp: new Date()
        }
      };
    }

    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    return updatedLeave;
  }

  async cancel(id: string, cancelledBy: string, cancellationReason: string): Promise<Leave> {
    const leave = await this.findOne(id);

    if (![LeaveStatus.PENDING, LeaveStatus.APPROVED].includes(leave.status)) {
      throw new BadRequestException('Only pending or approved leave requests can be cancelled');
    }

    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(
        id,
        {
          status: LeaveStatus.CANCELLED,
          cancelledBy,
          cancelledAt: new Date(),
          cancellationReason
        },
        { new: true }
      )
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    return updatedLeave;
  }

  async markReturn(id: string, returnDate: Date): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.APPROVED) {
      throw new BadRequestException('Only approved leaves can be marked as returned');
    }

    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(
        id,
        { returnDate },
        { new: true }
      )
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    return updatedLeave;
  }

  async requestExtension(
    id: string,
    extensionDays: number,
    extensionReason: string
  ): Promise<Leave> {
    const leave = await this.findOne(id);

    if (leave.status !== LeaveStatus.APPROVED) {
      throw new BadRequestException('Only approved leaves can be extended');
    }

    if (leave.extensionRequested) {
      throw new ConflictException('Extension already requested for this leave');
    }

    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(
        id,
        {
          extensionRequested: true,
          extensionDays,
          extensionReason,
          status: LeaveStatus.PENDING // Reset to pending for extension approval
        },
        { new: true }
      )
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    return updatedLeave;
  }

  async addComment(id: string, author: string, message: string): Promise<Leave> {
    const updatedLeave = await this.leaveModel
      .findByIdAndUpdate(
        id,
        {
          $push: {
            comments: {
              author,
              message,
              timestamp: new Date()
            }
          }
        },
        { new: true }
      )
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    if (!updatedLeave) {
      throw new NotFoundException('Leave request not found');
    }

    return updatedLeave;
  }

  async remove(id: string): Promise<void> {
    const leave = await this.findOne(id);

    if (leave.status === LeaveStatus.APPROVED) {
      throw new BadRequestException('Approved leaves cannot be deleted');
    }

    const result = await this.leaveModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('Leave request not found');
    }
  }

  async getLeaveBalance(employeeId: string, year?: number): Promise<any> {
    const currentYear = year || new Date().getFullYear();
    const startOfYear = new Date(currentYear, 0, 1);
    const endOfYear = new Date(currentYear, 11, 31);

    const leaves = await this.leaveModel
      .find({
        employeeId: new Types.ObjectId(employeeId),
        status: LeaveStatus.APPROVED,
        startDate: { $gte: startOfYear, $lte: endOfYear }
      })
      .exec();

    const leavesByType = leaves.reduce((acc, leave) => {
      if (!acc[leave.type]) {
        acc[leave.type] = 0;
      }
      acc[leave.type] += leave.totalDays;
      return acc;
    }, {});

    // Standard leave allocations (can be configured)
    const standardAllocations = {
      [LeaveType.ANNUAL]: 30,
      [LeaveType.SICK]: 15,
      [LeaveType.EMERGENCY]: 5,
      [LeaveType.MATERNITY]: 90,
      [LeaveType.PATERNITY]: 7,
      [LeaveType.STUDY]: 10,
      [LeaveType.HAJJ]: 21,
      [LeaveType.OTHER]: 0
    };

    const balance = {};
    Object.values(LeaveType).forEach(type => {
      const used = leavesByType[type] || 0;
      const allocated = standardAllocations[type] || 0;
      balance[type] = {
        allocated,
        used,
        remaining: Math.max(0, allocated - used)
      };
    });

    return {
      year: currentYear,
      balance,
      totalUsed: Object.values(leavesByType).reduce((sum: number, days: number) => sum + days, 0),
      totalAllocated: Object.values(standardAllocations).reduce((sum, days) => sum + days, 0)
    };
  }

  private buildQuery(filters?: any): any {
    const query: any = {};

    if (filters) {
      if (filters.employeeId) {
        query.employeeId = new Types.ObjectId(filters.employeeId);
      }
      if (filters.status) {
        query.status = filters.status;
      }
      if (filters.type) {
        query.type = filters.type;
      }
      if (filters.isEmergency !== undefined) {
        query.isEmergency = filters.isEmergency;
      }
      if (filters.startDate || filters.endDate) {
        query.startDate = {};
        if (filters.startDate) query.startDate.$gte = new Date(filters.startDate);
        if (filters.endDate) query.startDate.$lte = new Date(filters.endDate);
      }
      if (filters.approvedBy) {
        query.approvedBy = filters.approvedBy;
      }
      if (filters.requestedBy) {
        query.requestedBy = filters.requestedBy;
      }
    }

    return query;
  }
}

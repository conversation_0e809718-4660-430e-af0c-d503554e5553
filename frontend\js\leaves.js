// Leaves management functionality

// Leaves data
let leavesData = {
    leaves: [],
    currentPage: 1,
    totalPages: 1,
    pageSize: 10,
    totalCount: 0,
    filters: {
        status: '',
        type: '',
        employeeId: ''
    },
    summary: {
        pending: 0,
        approved: 0,
        rejected: 0
    }
};

// <PERSON>ad leaves data
async function loadLeavesData() {
    try {
        console.log('Loading leaves data...');
        
        // Load leaves summary
        await loadLeavesSummary();
        
        // Load leaves records
        await loadLeavesRecords();
        
        // Update UI
        updateLeavesTable();
        updateLeavesPagination();
        updateLeavesSummary();
        
        console.log('Leaves data loaded successfully');
    } catch (error) {
        console.error('Error loading leaves data:', error);
        loadMockLeavesData();
        updateLeavesTable();
        updateLeavesPagination();
        updateLeavesSummary();
    }
}

// Load leaves summary
async function loadLeavesSummary() {
    try {
        // Mock data for leaves summary
        leavesData.summary = {
            pending: 12,
            approved: 45,
            rejected: 3
        };
    } catch (error) {
        console.error('Error loading leaves summary:', error);
        leavesData.summary = { pending: 0, approved: 0, rejected: 0 };
    }
}

// Load leaves records
async function loadLeavesRecords() {
    try {
        const params = {
            page: leavesData.currentPage,
            limit: leavesData.pageSize,
            status: leavesData.filters.status,
            type: leavesData.filters.type,
            employeeId: leavesData.filters.employeeId
        };
        
        // Remove empty parameters
        Object.keys(params).forEach(key => {
            if (!params[key]) delete params[key];
        });
        
        const response = await apiService.getLeaves(params);
        
        if (response && response.success) {
            leavesData.leaves = response.data.leaves || [];
            leavesData.totalCount = response.data.totalCount || 0;
            leavesData.totalPages = Math.ceil(leavesData.totalCount / leavesData.pageSize);
        } else {
            loadMockLeavesData();
        }
    } catch (error) {
        console.error('Error loading leaves records:', error);
        loadMockLeavesData();
    }
}

// Load mock leaves data
function loadMockLeavesData() {
    const today = new Date();
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);
    const nextMonth = new Date(today);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    
    leavesData.leaves = [
        {
            id: 1,
            employeeId: 1,
            employeeName: 'أحمد محمد علي',
            type: 'ANNUAL',
            startDate: nextWeek.toISOString().split('T')[0],
            endDate: new Date(nextWeek.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            days: 7,
            status: 'PENDING',
            reason: 'إجازة سنوية',
            requestDate: today.toISOString().split('T')[0],
            approvedBy: null,
            approvedDate: null
        },
        {
            id: 2,
            employeeId: 2,
            employeeName: 'سارة أحمد خالد',
            type: 'SICK',
            startDate: today.toISOString().split('T')[0],
            endDate: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            days: 3,
            status: 'APPROVED',
            reason: 'إجازة مرضية',
            requestDate: new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            approvedBy: 'المدير العام',
            approvedDate: today.toISOString().split('T')[0]
        },
        {
            id: 3,
            employeeId: 3,
            employeeName: 'محمد علي حسن',
            type: 'EMERGENCY',
            startDate: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            endDate: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            days: 2,
            status: 'PENDING',
            reason: 'ظروف طارئة',
            requestDate: today.toISOString().split('T')[0],
            approvedBy: null,
            approvedDate: null
        },
        {
            id: 4,
            employeeId: 4,
            employeeName: 'فاطمة خالد محمد',
            type: 'MATERNITY',
            startDate: nextMonth.toISOString().split('T')[0],
            endDate: new Date(nextMonth.getTime() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            days: 90,
            status: 'APPROVED',
            reason: 'إجازة أمومة',
            requestDate: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            approvedBy: 'مدير الموارد البشرية',
            approvedDate: new Date(today.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        {
            id: 5,
            employeeId: 5,
            employeeName: 'عبدالله سعد الغامدي',
            type: 'ANNUAL',
            startDate: new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            endDate: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            days: 7,
            status: 'REJECTED',
            reason: 'إجازة سنوية',
            requestDate: new Date(today.getTime() - 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            approvedBy: 'المدير العام',
            approvedDate: new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            rejectionReason: 'تعارض مع جدول العمل'
        }
    ];
    
    leavesData.totalCount = leavesData.leaves.length;
    leavesData.totalPages = Math.ceil(leavesData.totalCount / leavesData.pageSize);
}

// Update leaves table
function updateLeavesTable() {
    const tableBody = document.getElementById('leavesTableBody');
    if (!tableBody) return;
    
    // Clear existing content
    tableBody.innerHTML = '';
    
    if (leavesData.leaves.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted" style="padding: 40px;">
                    <i class="fas fa-calendar-alt" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <p>لا توجد طلبات إجازة للعرض</p>
                </td>
            </tr>
        `;
        return;
    }
    
    // Create table rows
    const tableHTML = leavesData.leaves.map(leave => `
        <tr>
            <td><strong>${leave.employeeName}</strong></td>
            <td>
                <span class="badge ${getLeaveTypeBadgeClass(leave.type)}">
                    ${getLeaveTypeText(leave.type)}
                </span>
            </td>
            <td>${formatDate(leave.startDate)}</td>
            <td>${formatDate(leave.endDate)}</td>
            <td><strong>${leave.days}</strong></td>
            <td>
                <span class="badge ${getLeaveStatusBadgeClass(leave.status)}">
                    ${getLeaveStatusText(leave.status)}
                </span>
            </td>
            <td>
                <div class="btn-group" style="display: flex; gap: 4px;">
                    <button class="btn btn-sm btn-info" onclick="viewLeave(${leave.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${leave.status === 'PENDING' ? `
                        <button class="btn btn-sm btn-success" onclick="approveLeave(${leave.id})" title="موافقة">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="rejectLeave(${leave.id})" title="رفض">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = tableHTML;
}

// Get leave type badge class
function getLeaveTypeBadgeClass(type) {
    const classes = {
        'ANNUAL': 'badge-primary',
        'SICK': 'badge-warning',
        'EMERGENCY': 'badge-danger',
        'MATERNITY': 'badge-info',
        'PATERNITY': 'badge-info',
        'UNPAID': 'badge-secondary'
    };
    return classes[type] || 'badge-secondary';
}

// Get leave type text in Arabic
function getLeaveTypeText(type) {
    const texts = {
        'ANNUAL': 'سنوية',
        'SICK': 'مرضية',
        'EMERGENCY': 'طارئة',
        'MATERNITY': 'أمومة',
        'PATERNITY': 'أبوة',
        'UNPAID': 'بدون راتب'
    };
    return texts[type] || type;
}

// Get leave status badge class
function getLeaveStatusBadgeClass(status) {
    const classes = {
        'PENDING': 'badge-warning',
        'APPROVED': 'badge-success',
        'REJECTED': 'badge-danger',
        'CANCELLED': 'badge-secondary'
    };
    return classes[status] || 'badge-secondary';
}

// Get leave status text in Arabic
function getLeaveStatusText(status) {
    const texts = {
        'PENDING': 'معلق',
        'APPROVED': 'موافق عليه',
        'REJECTED': 'مرفوض',
        'CANCELLED': 'ملغي'
    };
    return texts[status] || status;
}

// Update leaves pagination
function updateLeavesPagination() {
    // Similar to other pagination functions
    const paginationContainer = document.querySelector('#leaves-page .pagination');
    if (!paginationContainer) return;
    
    // Clear existing content
    paginationContainer.innerHTML = '';
    
    if (leavesData.totalPages <= 1) return;
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <button ${leavesData.currentPage === 1 ? 'disabled' : ''} 
                onclick="changeLeavesPage(${leavesData.currentPage - 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // Page numbers
    const startPage = Math.max(1, leavesData.currentPage - 2);
    const endPage = Math.min(leavesData.totalPages, leavesData.currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button ${i === leavesData.currentPage ? 'class="active"' : ''} 
                    onclick="changeLeavesPage(${i})">
                ${i}
            </button>
        `;
    }
    
    // Next button
    paginationHTML += `
        <button ${leavesData.currentPage === leavesData.totalPages ? 'disabled' : ''} 
                onclick="changeLeavesPage(${leavesData.currentPage + 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    paginationContainer.innerHTML = paginationHTML;
}

// Update leaves summary
function updateLeavesSummary() {
    const pendingElement = document.getElementById('pendingLeavesCount');
    const approvedElement = document.getElementById('approvedLeavesCount');
    const rejectedElement = document.getElementById('rejectedLeavesCount');
    
    if (pendingElement) {
        pendingElement.textContent = leavesData.summary.pending;
    }
    
    if (approvedElement) {
        approvedElement.textContent = leavesData.summary.approved;
    }
    
    if (rejectedElement) {
        rejectedElement.textContent = leavesData.summary.rejected;
    }
}

// Change leaves page
function changeLeavesPage(page) {
    if (page < 1 || page > leavesData.totalPages) return;
    
    leavesData.currentPage = page;
    loadLeavesData();
}

// Search leaves
function searchLeaves(searchTerm) {
    console.log('Searching leaves for:', searchTerm);
    // Implementation would filter the leaves records
}

// Filter leaves
function filterLeaves(filterType, filterValue) {
    console.log('Filtering leaves by:', filterType, filterValue);
    // Implementation would apply filters
}

// View leave details
function viewLeave(leaveId) {
    const leave = leavesData.leaves.find(l => l.id === leaveId);
    if (!leave) {
        showError('لم يتم العثور على طلب الإجازة');
        return;
    }
    
    console.log('View leave details for:', leave.employeeName);
    showInfo(`عرض تفاصيل إجازة: ${leave.employeeName}`);
}

// Approve leave
async function approveLeave(leaveId) {
    const leave = leavesData.leaves.find(l => l.id === leaveId);
    if (!leave) {
        showError('لم يتم العثور على طلب الإجازة');
        return;
    }
    
    if (!confirm(`هل أنت متأكد من الموافقة على إجازة ${leave.employeeName}؟`)) {
        return;
    }
    
    try {
        showLoading(true);
        
        const response = await apiService.approveLeave(leaveId);
        
        if (response && response.success) {
            showSuccess('تم الموافقة على الإجازة بنجاح');
            loadLeavesData();
        } else {
            showError('فشل في الموافقة على الإجازة');
        }
    } catch (error) {
        console.error('Error approving leave:', error);
        showError('خطأ في الموافقة على الإجازة');
    } finally {
        showLoading(false);
    }
}

// Reject leave
async function rejectLeave(leaveId) {
    const leave = leavesData.leaves.find(l => l.id === leaveId);
    if (!leave) {
        showError('لم يتم العثور على طلب الإجازة');
        return;
    }
    
    const reason = prompt(`سبب رفض إجازة ${leave.employeeName}:`);
    if (!reason) return;
    
    try {
        showLoading(true);
        
        const response = await apiService.rejectLeave(leaveId, reason);
        
        if (response && response.success) {
            showSuccess('تم رفض الإجازة بنجاح');
            loadLeavesData();
        } else {
            showError('فشل في رفض الإجازة');
        }
    } catch (error) {
        console.error('Error rejecting leave:', error);
        showError('خطأ في رفض الإجازة');
    } finally {
        showLoading(false);
    }
}

// Show add leave modal
function showAddLeaveModal() {
    console.log('Show add leave modal');
    showInfo('سيتم إضافة نافذة طلب إجازة جديدة قريباً');
}

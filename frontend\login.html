<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <!-- Background Pattern -->
        <div class="login-background">
            <div class="pattern-overlay"></div>
        </div>

        <!-- Login Card -->
        <div class="login-card">
            <!-- Logo Section -->
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-building"></i>
                </div>
                <h1>نظام إدارة الموظفين</h1>
                <p>نظام شامل لإدارة الموظفين والموارد البشرية</p>
            </div>

            <!-- Login Form -->
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        required 
                        placeholder="أدخل اسم المستخدم"
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required 
                            placeholder="أدخل كلمة المرور"
                            autocomplete="current-password"
                        >
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>

                <!-- Error Message -->
                <div class="error-message" id="errorMessage" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorText"></span>
                </div>

                <!-- Success Message -->
                <div class="success-message" id="successMessage" style="display: none;">
                    <i class="fas fa-check-circle"></i>
                    <span id="successText"></span>
                </div>
            </form>

            <!-- Demo Credentials -->
            <div class="demo-credentials">
                <h4>
                    <i class="fas fa-info-circle"></i>
                    بيانات تجريبية
                </h4>
                <div class="credentials-grid">
                    <div class="credential-item">
                        <strong>المدير العام:</strong>
                        <span>admin / admin123</span>
                    </div>
                    <div class="credential-item">
                        <strong>مدير الموارد البشرية:</strong>
                        <span>hr_manager / hr123</span>
                    </div>
                    <div class="credential-item">
                        <strong>موظف:</strong>
                        <span>employee / emp123</span>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="login-footer">
                <p>&copy; 2024 نظام إدارة الموظفين. جميع الحقوق محفوظة.</p>
                <div class="footer-links">
                    <a href="#">الدعم الفني</a>
                    <a href="#">سياسة الخصوصية</a>
                    <a href="#">شروط الاستخدام</a>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="system-status" id="systemStatus">
            <div class="status-indicator">
                <i class="fas fa-circle" id="statusIcon"></i>
                <span id="statusText">جاري فحص حالة النظام...</span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري تسجيل الدخول...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Check system status on load
        window.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            
            // Auto-fill demo credentials
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
        });

        // Check system status
        async function checkSystemStatus() {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            
            try {
                const response = await fetch(`${API_CONFIG.BASE_URL}/health`);
                const data = await response.json();
                
                if (data.success) {
                    statusIcon.className = 'fas fa-circle status-online';
                    statusText.textContent = 'النظام متصل ويعمل بشكل طبيعي';
                } else {
                    throw new Error('System check failed');
                }
            } catch (error) {
                statusIcon.className = 'fas fa-circle status-offline';
                statusText.textContent = 'خطأ في الاتصال بالنظام';
            }
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Show loading
            showLoading(true);
            hideMessages();
            
            try {
                const response = await fetch(`${API_CONFIG.BASE_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Store authentication data
                    localStorage.setItem('authToken', data.data.token);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    localStorage.setItem('userData', JSON.stringify(data.data.user));
                    
                    if (rememberMe) {
                        localStorage.setItem('rememberLogin', 'true');
                    }
                    
                    // Show success message
                    showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');
                    
                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                    
                } else {
                    showError(data.message || 'فشل في تسجيل الدخول');
                }
                
            } catch (error) {
                console.error('Login error:', error);
                showError('خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.');
            } finally {
                showLoading(false);
            }
        });

        // Show/hide loading
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            const loginBtn = document.getElementById('loginBtn');
            
            if (show) {
                overlay.style.display = 'flex';
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            } else {
                overlay.style.display = 'none';
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
            }
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorDiv.style.display = 'flex';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            
            successText.textContent = message;
            successDiv.style.display = 'flex';
        }

        // Hide all messages
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // Check if user is already logged in
        if (localStorage.getItem('authToken')) {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>

import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Attendance, AttendanceDocument } from './schemas/attendance.schema';
import { AttendanceStatus, AttendanceMethod } from '@shared/types/employee.types';

export interface CreateAttendanceDto {
  employeeId: string;
  date: Date;
  checkIn?: Date;
  checkOut?: Date;
  status: AttendanceStatus;
  notes?: string;
  location?: string;
  method: AttendanceMethod;
}

export interface UpdateAttendanceDto {
  checkIn?: Date;
  checkOut?: Date;
  status?: AttendanceStatus;
  notes?: string;
  location?: string;
}

@Injectable()
export class AttendanceService {
  constructor(
    @InjectModel(Attendance.name) private attendanceModel: Model<AttendanceDocument>,
  ) {}

  async create(createAttendanceDto: CreateAttendanceDto, recordedBy: string): Promise<Attendance> {
    // Check if attendance already exists for this employee on this date
    const existingAttendance = await this.attendanceModel.findOne({
      employeeId: new Types.ObjectId(createAttendanceDto.employeeId),
      date: {
        $gte: new Date(createAttendanceDto.date.setHours(0, 0, 0, 0)),
        $lt: new Date(createAttendanceDto.date.setHours(23, 59, 59, 999))
      }
    });

    if (existingAttendance) {
      throw new ConflictException('Attendance record already exists for this date');
    }

    const attendance = new this.attendanceModel({
      ...createAttendanceDto,
      employeeId: new Types.ObjectId(createAttendanceDto.employeeId),
      recordedBy
    });

    return attendance.save();
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    filters?: any
  ): Promise<{ attendance: Attendance[], total: number }> {
    const skip = (page - 1) * limit;
    const query = this.buildQuery(filters);

    const [attendance, total] = await Promise.all([
      this.attendanceModel
        .find(query)
        .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
        .skip(skip)
        .limit(limit)
        .sort({ date: -1 })
        .exec(),
      this.attendanceModel.countDocuments(query)
    ]);

    return { attendance, total };
  }

  async findOne(id: string): Promise<Attendance> {
    const attendance = await this.attendanceModel
      .findById(id)
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    if (!attendance) {
      throw new NotFoundException('Attendance record not found');
    }

    return attendance;
  }

  async findByEmployee(
    employeeId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<Attendance[]> {
    const query: any = { employeeId: new Types.ObjectId(employeeId) };

    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = startDate;
      if (endDate) query.date.$lte = endDate;
    }

    return this.attendanceModel
      .find(query)
      .sort({ date: -1 })
      .exec();
  }

  async update(id: string, updateAttendanceDto: UpdateAttendanceDto): Promise<Attendance> {
    const attendance = await this.attendanceModel
      .findByIdAndUpdate(id, updateAttendanceDto, { new: true })
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .exec();

    if (!attendance) {
      throw new NotFoundException('Attendance record not found');
    }

    return attendance;
  }

  async remove(id: string): Promise<void> {
    const result = await this.attendanceModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('Attendance record not found');
    }
  }

  async checkIn(employeeId: string, method: AttendanceMethod, location?: string, recordedBy?: string): Promise<Attendance> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Check if already checked in today
    const existingAttendance = await this.attendanceModel.findOne({
      employeeId: new Types.ObjectId(employeeId),
      date: { $gte: today, $lt: tomorrow }
    });

    if (existingAttendance) {
      if (existingAttendance.checkIn) {
        throw new ConflictException('Employee already checked in today');
      }
      
      // Update existing record with check-in
      existingAttendance.checkIn = new Date();
      existingAttendance.method = method;
      if (location) existingAttendance.location = location;
      existingAttendance.status = AttendanceStatus.PRESENT;
      
      return existingAttendance.save();
    }

    // Create new attendance record
    const attendance = new this.attendanceModel({
      employeeId: new Types.ObjectId(employeeId),
      date: today,
      checkIn: new Date(),
      status: AttendanceStatus.PRESENT,
      method,
      location,
      recordedBy
    });

    return attendance.save();
  }

  async checkOut(employeeId: string, method: AttendanceMethod, location?: string): Promise<Attendance> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const attendance = await this.attendanceModel.findOne({
      employeeId: new Types.ObjectId(employeeId),
      date: { $gte: today, $lt: tomorrow }
    });

    if (!attendance) {
      throw new NotFoundException('No check-in record found for today');
    }

    if (!attendance.checkIn) {
      throw new BadRequestException('Employee must check in first');
    }

    if (attendance.checkOut) {
      throw new ConflictException('Employee already checked out today');
    }

    attendance.checkOut = new Date();
    attendance.method = method;
    if (location) attendance.location = location;

    return attendance.save();
  }

  async getAttendanceSummary(
    employeeId: string,
    startDate: Date,
    endDate: Date
  ): Promise<any> {
    const attendance = await this.findByEmployee(employeeId, startDate, endDate);
    
    const summary = {
      totalDays: attendance.length,
      presentDays: attendance.filter(a => a.status === AttendanceStatus.PRESENT).length,
      absentDays: attendance.filter(a => a.status === AttendanceStatus.ABSENT).length,
      lateDays: attendance.filter(a => a.isLate).length,
      earlyLeaveDays: attendance.filter(a => a.isEarlyLeave).length,
      totalWorkingHours: attendance.reduce((sum, a) => sum + (a.workingHours || 0), 0),
      totalOvertimeHours: attendance.reduce((sum, a) => sum + (a.overtimeHours || 0), 0),
      attendanceRate: 0
    };

    if (summary.totalDays > 0) {
      summary.attendanceRate = (summary.presentDays / summary.totalDays) * 100;
    }

    return summary;
  }

  async getDailyAttendanceReport(date: Date): Promise<any> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const attendance = await this.attendanceModel
      .find({ date: { $gte: startOfDay, $lte: endOfDay } })
      .populate('employeeId', 'personalInfo.fullName personalInfo.militaryNumber jobInfo.unit jobInfo.rank')
      .sort({ 'employeeId.jobInfo.unit': 1, 'employeeId.personalInfo.fullName': 1 })
      .exec();

    const summary = {
      date,
      totalEmployees: attendance.length,
      present: attendance.filter(a => a.status === AttendanceStatus.PRESENT).length,
      absent: attendance.filter(a => a.status === AttendanceStatus.ABSENT).length,
      late: attendance.filter(a => a.isLate).length,
      earlyLeave: attendance.filter(a => a.isEarlyLeave).length,
      onLeave: attendance.filter(a => a.status === AttendanceStatus.ON_LEAVE).length,
      records: attendance
    };

    return summary;
  }

  private buildQuery(filters?: any): any {
    const query: any = {};

    if (filters) {
      if (filters.employeeId) {
        query.employeeId = new Types.ObjectId(filters.employeeId);
      }
      if (filters.status) {
        query.status = filters.status;
      }
      if (filters.method) {
        query.method = filters.method;
      }
      if (filters.isLate !== undefined) {
        query.isLate = filters.isLate;
      }
      if (filters.isEarlyLeave !== undefined) {
        query.isEarlyLeave = filters.isEarlyLeave;
      }
      if (filters.startDate || filters.endDate) {
        query.date = {};
        if (filters.startDate) query.date.$gte = new Date(filters.startDate);
        if (filters.endDate) query.date.$lte = new Date(filters.endDate);
      }
      if (filters.location) {
        query.location = { $regex: filters.location, $options: 'i' };
      }
    }

    return query;
  }
}

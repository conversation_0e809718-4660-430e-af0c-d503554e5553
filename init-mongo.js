// MongoDB Initialization Script
db = db.getSiblingDB('employee_management');

// Create collections
db.createCollection('employees');
db.createCollection('users');
db.createCollection('reports');
db.createCollection('attendance');
db.createCollection('leaves');
db.createCollection('punishments');
db.createCollection('rewards');

// Create indexes for better performance
db.employees.createIndex({ "militaryNumber": 1 }, { unique: true });
db.employees.createIndex({ "nationalId": 1 }, { unique: true });
db.employees.createIndex({ "email": 1 }, { unique: true });
db.employees.createIndex({ "unit": 1 });
db.employees.createIndex({ "rank": 1 });
db.employees.createIndex({ "status": 1 });

db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "role": 1 });

db.attendance.createIndex({ "employeeId": 1 });
db.attendance.createIndex({ "date": 1 });
db.attendance.createIndex({ "employeeId": 1, "date": 1 });

db.leaves.createIndex({ "employeeId": 1 });
db.leaves.createIndex({ "startDate": 1 });
db.leaves.createIndex({ "status": 1 });

// Insert default admin user
db.users.insertOne({
  username: "admin",
  email: "<EMAIL>",
  password: "$2b$10$8K1p/a0dRTlNqNzNNtyaOeJ67QNMDaFWK.70YjYxnkf7Y2jjwW0YG", // password: admin123
  role: "SYSTEM_ADMIN",
  permissions: [
    "READ_ALL",
    "WRITE_ALL",
    "DELETE_ALL",
    "MANAGE_USERS",
    "MANAGE_SYSTEM"
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Insert sample ranks
db.ranks.insertMany([
  { name: "جندي", nameEn: "Private", level: 1, category: "soldiers" },
  { name: "جندي أول", nameEn: "Private First Class", level: 2, category: "soldiers" },
  { name: "عريف", nameEn: "Corporal", level: 3, category: "soldiers" },
  { name: "رقيب", nameEn: "Sergeant", level: 4, category: "ncos" },
  { name: "رقيب أول", nameEn: "Staff Sergeant", level: 5, category: "ncos" },
  { name: "رئيس رقباء", nameEn: "Sergeant Major", level: 6, category: "ncos" },
  { name: "ملازم", nameEn: "Lieutenant", level: 7, category: "officers" },
  { name: "ملازم أول", nameEn: "First Lieutenant", level: 8, category: "officers" },
  { name: "نقيب", nameEn: "Captain", level: 9, category: "officers" },
  { name: "رائد", nameEn: "Major", level: 10, category: "officers" },
  { name: "مقدم", nameEn: "Lieutenant Colonel", level: 11, category: "officers" },
  { name: "عقيد", nameEn: "Colonel", level: 12, category: "officers" },
  { name: "عميد", nameEn: "Brigadier General", level: 13, category: "generals" },
  { name: "لواء", nameEn: "Major General", level: 14, category: "generals" }
]);

// Insert sample units
db.units.insertMany([
  { name: "الوحدة الأولى", nameEn: "First Unit", code: "UNIT001", location: "الرياض" },
  { name: "الوحدة الثانية", nameEn: "Second Unit", code: "UNIT002", location: "جدة" },
  { name: "الوحدة الثالثة", nameEn: "Third Unit", code: "UNIT003", location: "الدمام" },
  { name: "القيادة العامة", nameEn: "General Command", code: "GEN001", location: "الرياض" },
  { name: "وحدة الإمداد", nameEn: "Supply Unit", code: "SUP001", location: "الرياض" }
]);

print("Database initialized successfully with collections, indexes, and sample data!");
